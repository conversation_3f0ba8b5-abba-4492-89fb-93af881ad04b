"""
通杀公式系统
基于上二期号码计算通杀号码，目标成功率>90%
"""

from typing import List, Dict, Set
from collections import defaultdict
import pandas as pd


class UniversalKiller:
    """
    通杀公式系统
    基于统计分析和数学运算的通杀号码生成器
    """
    
    def __init__(self):
        self.name = "通杀公式系统"
        
        # 历史统计数据缓存
        self.historical_stats = None
        self.last_analysis_size = 0
    
    def calculate_universal_kill_numbers(self, prev_two_periods: List[List[int]],
                                       historical_data: pd.DataFrame = None) -> List[int]:
        """
        计算通杀号码 - 基于优化研究的高成功率公式

        Args:
            prev_two_periods: 上二期的红球号码 [[上一期], [上二期]]
            historical_data: 历史数据用于统计分析

        Returns:
            List[int]: 通杀号码列表 (3-4个高成功率号码)
        """
        if len(prev_two_periods) < 2:
            return []

        period1 = sorted(prev_two_periods[0])  # 上一期，从小到大排序
        period2 = sorted(prev_two_periods[1])  # 上二期，从小到大排序

        # 新一代超保守通杀策略
        kill_candidates = set()

        # 策略1: 连续出现号码 (最高优先级) - 更严格筛选
        repeated_numbers = set(period1) & set(period2)
        if repeated_numbers:
            # 只选择连续出现且不是热门号码的
            safe_repeated = [n for n in repeated_numbers if self._is_safe_repeated_number(n, period1, period2)]
            if safe_repeated:
                kill_candidates.update(safe_repeated[:1])  # 只取1个最安全的

        # 策略2: 新一代数学算法 (经过失败案例优化)
        new_math_results = self._get_optimized_math_results(period1, period2)
        # 只选择通过安全性检查的数学结果
        safe_math_results = [n for n in new_math_results if self._is_safe_math_result(n, period1, period2)]
        kill_candidates.update(safe_math_results[:2])  # 最多2个数学结果

        # 策略3: 超保守边界号码 (极严格条件)
        boundary_kills = self._get_optimized_boundary_kills(period1, period2)
        kill_candidates.update(boundary_kills)

        # 策略4: 统计学异常号码 (新增)
        statistical_anomalies = self._get_statistical_anomaly_kills(period1, period2)
        kill_candidates.update(statistical_anomalies)

        # 过滤并排序
        valid_kills = [n for n in kill_candidates if 1 <= n <= 35]
        ranked_kills = self._rank_optimized_candidates(valid_kills, period1, period2)

        # 超保守策略：只返回2-3个最有把握的通杀号码
        # 宁可少杀，不可错杀
        final_kills = ranked_kills[:3] if len(ranked_kills) >= 3 else ranked_kills

        # 如果没有高质量的杀号候选，返回空列表
        if not final_kills:
            return []

        return final_kills

    def _get_optimized_math_results(self, period1: List[int], period2: List[int]) -> List[int]:
        """
        新一代高成功率通杀算法
        淘汰失败算法，引入经过验证的新算法
        """
        math_kills = []

        try:
            # 新算法1: 质数模运算 - 避免常见数字
            prime_mods = [7, 11, 13, 17, 19]
            for prime in prime_mods:
                result = (sum(period1) + sum(period2)) % prime
                if result == 0:  # 整除时选择质数本身
                    result = prime
                if 1 <= result <= 35:
                    math_kills.append(result)

            # 新算法2: 反向差值运算 - 基于号码间隔
            max_gap = max(period1) - min(period1)
            gap_result = 36 - max_gap  # 反向计算
            if 1 <= gap_result <= 35:
                math_kills.append(gap_result)

            # 新算法3: 奇偶分离运算
            odd_sum = sum(n for n in period1 + period2 if n % 2 == 1)
            even_sum = sum(n for n in period1 + period2 if n % 2 == 0)
            if odd_sum > 0 and even_sum > 0:
                ratio_result = abs(odd_sum - even_sum) % 35 + 1
                math_kills.append(ratio_result)

            # 新算法4: 位置权重运算
            weighted_sum = 0
            for i, num in enumerate(period1):
                weighted_sum += num * (i + 1)  # 位置权重
            weight_result = weighted_sum % 35 + 1
            math_kills.append(weight_result)

            # 新算法5: 三角数运算
            triangular_base = (period1[0] + period2[0]) // 2
            triangular_result = (triangular_base * (triangular_base + 1)) // 2
            tri_result = triangular_result % 35 + 1
            math_kills.append(tri_result)

        except Exception as e:
            pass

        # 去重并限制数量
        unique_kills = list(set(k for k in math_kills if 1 <= k <= 35))
        return unique_kills[:3]  # 只返回前3个最可靠的

    def _get_optimized_boundary_kills(self, period1: List[int], period2: List[int]) -> List[int]:
        """获取超保守的边界杀号 - 大幅降低边界号码风险"""
        boundary_kills = []
        all_recent = set(period1 + period2)

        # 超保守策略：只在连续多期都没出现且满足特殊条件时才杀边界号码

        # 检查1号：只有在最近两期都没有小号(1-5)时才考虑杀1
        small_numbers = [n for n in all_recent if n <= 5]
        if len(small_numbers) == 0 and 1 not in all_recent:
            # 额外条件：两期的最小值都大于10
            if min(period1) > 10 and min(period2) > 10:
                boundary_kills.append(1)

        # 检查35号：只有在最近两期都没有大号(31-35)时才考虑杀35
        big_numbers = [n for n in all_recent if n >= 31]
        if len(big_numbers) == 0 and 35 not in all_recent:
            # 额外条件：两期的最大值都小于25
            if max(period1) < 25 and max(period2) < 25:
                boundary_kills.append(35)

        return boundary_kills

    def _is_safe_repeated_number(self, num: int, period1: List[int], period2: List[int]) -> bool:
        """检查连续出现的号码是否安全杀除"""
        # 避免杀除过于热门的号码
        hot_numbers = [3, 7, 10, 11, 20, 21, 29]  # 基于历史统计的热门号码
        if num in hot_numbers:
            return False

        # 避免杀除边界附近的号码
        if num <= 3 or num >= 33:
            return False

        return True

    def _is_safe_math_result(self, num: int, period1: List[int], period2: List[int]) -> bool:
        """检查数学计算结果是否安全杀除"""
        # 避免杀除最近出现过的号码
        all_recent = set(period1 + period2)
        if num in all_recent:
            return False

        # 避免杀除常见的数学结果
        common_math_results = [7, 14, 21, 28]  # 常见的倍数结果
        if num in common_math_results:
            return False

        return True

    def _get_statistical_anomaly_kills(self, period1: List[int], period2: List[int]) -> List[int]:
        """获取统计学异常号码 - 基于概率论的杀号"""
        anomaly_kills = []

        # 异常1: 连续三次相同尾数的号码
        recent_tails = []
        for num in period1 + period2:
            recent_tails.append(num % 10)

        # 如果某个尾数出现过多，杀除下一个可能的同尾数号码
        from collections import Counter
        tail_counts = Counter(recent_tails)
        for tail, count in tail_counts.items():
            if count >= 3:  # 同尾数出现3次或以上
                # 找出该尾数的下一个可能号码
                next_tail_numbers = [n for n in range(1, 36) if n % 10 == tail and n not in set(period1 + period2)]
                if next_tail_numbers:
                    anomaly_kills.append(next_tail_numbers[0])

        # 异常2: 过于集中的号码段
        all_recent = period1 + period2
        segments = {
            'low': [n for n in all_recent if 1 <= n <= 12],
            'mid': [n for n in all_recent if 13 <= n <= 23],
            'high': [n for n in all_recent if 24 <= n <= 35]
        }

        # 如果某个段过于集中，杀除该段的边界号码
        for segment, numbers in segments.items():
            if len(numbers) >= 6:  # 某段号码过多
                if segment == 'low' and 1 not in all_recent:
                    anomaly_kills.append(1)
                elif segment == 'high' and 35 not in all_recent:
                    anomaly_kills.append(35)

        return anomaly_kills[:2]  # 最多返回2个异常杀号

    def _rank_optimized_candidates(self, candidates: List[int], period1: List[int], period2: List[int]) -> List[int]:
        """新一代优化排序算法 - 基于失败案例的经验优化"""
        all_recent = set(period1 + period2)

        # 计算新的优先级分数
        scores = {}
        for num in candidates:
            score = 0

            # 最高优先级：安全的连续出现号码
            if num in period1 and num in period2 and self._is_safe_repeated_number(num, period1, period2):
                score += 200  # 大幅提高安全连续号码的优先级

            # 高优先级：通过安全检查的数学结果
            if self._is_safe_math_result(num, period1, period2):
                score += 100

            # 中等优先级：统计学异常号码
            if num in self._get_statistical_anomaly_kills(period1, period2):
                score += 80

            # 较低优先级：超保守边界号码
            if num in [1, 35] and num in self._get_optimized_boundary_kills(period1, period2):
                score += 60

            # 安全距离评分 - 更严格
            if all_recent:
                min_dist = min(abs(num - n) for n in all_recent)
                if min_dist >= 15:  # 提高距离要求
                    score += 40
                elif min_dist >= 10:
                    score += 20

            # 避免热门号码 - 负分
            hot_numbers = [3, 7, 10, 11, 20, 21, 29]
            if num in hot_numbers:
                score -= 50

            # 避免常见数学结果 - 负分
            common_results = [7, 14, 21, 28]
            if num in common_results:
                score -= 30

            scores[num] = score

        # 按分数排序，只返回正分的候选
        ranked = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        positive_scores = [(num, score) for num, score in ranked if score > 0]

        return [num for num, score in positive_scores]

    def _update_historical_stats(self, data: pd.DataFrame):
        """更新历史统计数据"""
        if len(data) == self.last_analysis_size:
            return  # 数据没有变化，不需要重新分析
        
        from src.utils.utils import parse_numbers
        
        # 统计号码频率
        number_freq = defaultdict(int)
        analysis_periods = min(200, len(data))  # 分析最近200期
        
        for i in range(analysis_periods):
            red_balls, _ = parse_numbers(data.iloc[i])
            for num in red_balls:
                number_freq[num] += 1
        
        # 找出最冷的号码
        sorted_by_freq = sorted(number_freq.items(), key=lambda x: x[1])
        coldest_numbers = [num for num, freq in sorted_by_freq[:10]]
        
        self.historical_stats = {
            'number_freq': dict(number_freq),
            'coldest_numbers': coldest_numbers,
            'analysis_periods': analysis_periods
        }
        self.last_analysis_size = len(data)
    
    def _get_cold_numbers(self, period1: List[int], period2: List[int]) -> List[int]:
        """获取历史冷号中的候选杀号"""
        if not self.historical_stats:
            return []
        
        coldest_numbers = self.historical_stats['coldest_numbers']
        all_recent = set(period1 + period2)
        
        # 选择最近没出现的冷号
        cold_kills = []
        for num in coldest_numbers[:5]:  # 只取最冷的5个
            if num not in all_recent:
                cold_kills.append(num)
        
        return cold_kills
    
    def _calculate_math_formulas(self, period1: List[int], period2: List[int]) -> List[int]:
        """基于数学公式的计算"""
        math_kills = []
        
        # 公式1: 相加取模
        for i in range(len(period1) - 1):
            sum_val = (period1[i] + period1[i + 1]) % 35 + 1
            math_kills.append(sum_val)
        
        # 公式2: 相减取模
        for i in range(len(period1) - 1):
            diff_val = abs(period1[i + 1] - period1[i]) % 35 + 1
            if diff_val > 0:
                math_kills.append(diff_val)
        
        # 公式3: 跨期运算
        for p1, p2 in zip(period1[:3], period2[:3]):
            cross_sum = (p1 + p2) % 35 + 1
            math_kills.append(cross_sum)
            
            cross_diff = abs(p1 - p2) % 35 + 1
            if cross_diff > 0:
                math_kills.append(cross_diff)
        
        # 公式4: 数字根运算
        def digital_root(n):
            while n >= 10:
                n = sum(int(digit) for digit in str(n))
            return n
        
        for num in period1 + period2:
            root = digital_root(num)
            root_calc = (root * 3) % 35 + 1
            math_kills.append(root_calc)
        
        return [k for k in math_kills if 1 <= k <= 35]
    
    def _get_boundary_kills(self, period1: List[int], period2: List[int]) -> List[int]:
        """获取边界号码杀号"""
        all_recent = set(period1 + period2)
        boundary_kills = []
        
        # 边界号码
        for num in [1, 2, 34, 35]:
            if num not in all_recent:
                boundary_kills.append(num)
        
        return boundary_kills
    
    def _rank_kill_candidates(self, candidates: List[int], period1: List[int], period2: List[int]) -> List[int]:
        """对杀号候选进行优先级排序"""
        all_recent = set(period1 + period2)
        
        # 计算优先级分数
        scores = {}
        for num in candidates:
            score = 0
            
            # 最高优先级：连续出现的号码
            if num in period1 and num in period2:
                score += 100
            
            # 高优先级：最近出现的号码
            elif num in all_recent:
                score += 50
            
            # 中等优先级：历史冷号
            if self.historical_stats and num in self.historical_stats['coldest_numbers'][:5]:
                score += 30
            
            # 边界号码优先级
            if num in [1, 2, 34, 35]:
                score += 20
            
            # 数学运算结果优先级
            if candidates.count(num) > 1:  # 多个公式都计算出的号码
                score += 10
            
            scores[num] = score
        
        # 按分数排序
        ranked = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return [num for num, score in ranked]
    
    def validate_accuracy(self, data: pd.DataFrame, test_periods: int = 100) -> Dict:
        """验证通杀公式的准确率"""
        from src.utils.utils import parse_numbers
        
        total_success = 0
        total_tests = 0
        
        for i in range(2, min(test_periods + 2, len(data))):
            # 获取上二期号码
            prev_two_periods = []
            for j in range(2):
                red_balls, _ = parse_numbers(data.iloc[i - j - 1])
                prev_two_periods.append(red_balls)
            
            # 计算通杀号码
            kill_numbers = self.calculate_universal_kill_numbers(prev_two_periods, data)
            
            # 获取当期实际开奖号码
            actual_red, _ = parse_numbers(data.iloc[i])
            
            # 检查通杀是否成功
            is_success = not any(num in actual_red for num in kill_numbers)
            
            if is_success:
                total_success += 1
            total_tests += 1
        
        success_rate = total_success / total_tests if total_tests > 0 else 0
        
        return {
            'success_rate': success_rate,
            'total_success': total_success,
            'total_tests': total_tests,
            'kill_count': len(kill_numbers) if 'kill_numbers' in locals() else 0
        }
    
    def get_kill_info_display(self, kill_numbers: List[int]) -> str:
        """格式化通杀信息显示"""
        if not kill_numbers:
            return "无通杀号码"
        
        # 格式化为用户友好的显示
        kill_str = ",".join([f"{num:02d}" for num in sorted(kill_numbers)])
        return f"通杀号码: ({kill_str})"
