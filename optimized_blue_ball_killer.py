#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的蓝球杀号算法
基于分析结果，重新设计蓝球杀号策略
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict

class OptimizedBlueBallKiller:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        
        # 基于分析的危险号码（经常被误杀）
        self.dangerous_numbers = [6, 4, 2]
        
        # 基于分析的热号（避免杀掉）
        self.hot_numbers = [1, 10, 7, 3, 8, 4]
        
    def calculate_optimized_blue_kills(self, recent_periods: List[List[int]], target_count: int = 3) -> List[int]:
        """
        优化的蓝球杀号策略
        
        核心原则：
        1. 减少杀号数量：从5个减少到3个
        2. 基于位置分析
        3. 避免杀掉热号和危险号码
        4. 重视近期趋势
        """
        if len(recent_periods) < 2:
            return [11, 12, 5]  # 默认冷号
        
        print(f"🔵 优化蓝球杀号策略 (目标{target_count}个):")
        
        # 策略1: 位置分析杀号
        position_kills = self._get_position_based_kills()
        print(f"  位置分析杀号: {position_kills}")
        
        # 策略2: 频率分析杀号
        frequency_kills = self._get_frequency_based_kills()
        print(f"  频率分析杀号: {frequency_kills}")
        
        # 策略3: 近期趋势杀号
        trend_kills = self._get_trend_based_kills(recent_periods)
        print(f"  趋势分析杀号: {trend_kills}")
        
        # 策略4: 避开危险区域
        recent_active = self._get_recent_active_numbers(recent_periods)
        print(f"  近期活跃: {sorted(recent_active)}")
        print(f"  危险号码: {self.dangerous_numbers}")
        print(f"  热号: {self.hot_numbers}")
        
        # 综合评分
        kill_scores = defaultdict(float)
        
        # 位置分析权重40%
        for i, num in enumerate(position_kills):
            kill_scores[num] += 0.4 * (len(position_kills) - i) / len(position_kills)
        
        # 频率分析权重30%
        for i, num in enumerate(frequency_kills):
            kill_scores[num] += 0.3 * (len(frequency_kills) - i) / len(frequency_kills)
        
        # 趋势分析权重30%
        for i, num in enumerate(trend_kills):
            kill_scores[num] += 0.3 * (len(trend_kills) - i) / len(trend_kills)
        
        # 应用过滤规则
        filtered_scores = self._apply_smart_filters(kill_scores, recent_active)
        
        # 选择得分最高的杀号
        sorted_kills = sorted(filtered_scores.items(), key=lambda x: x[1], reverse=True)
        final_kills = [num for num, score in sorted_kills[:target_count]]
        
        print(f"  最终杀号: {sorted(final_kills)} (共{len(final_kills)}个)")
        
        return final_kills
    
    def _get_position_based_kills(self) -> List[int]:
        """基于位置分析的杀号"""
        try:
            position_data = {1: [], 2: []}
            
            # 分析最近50期的位置数据
            for i, row in self.data.head(50).iterrows():
                from test_kill_algorithm import parse_numbers
                _, blue_balls = parse_numbers(row)
                
                if isinstance(blue_balls, list) and len(blue_balls) == 2:
                    sorted_blues = sorted(blue_balls)
                    position_data[1].append(sorted_blues[0])
                    position_data[2].append(sorted_blues[1])
                elif isinstance(blue_balls, int):
                    position_data[1].append(blue_balls)
            
            position_kills = []
            
            # 位置1杀号：很少出现大号
            if position_data[1]:
                pos1_counter = Counter(position_data[1])
                # 杀掉在位置1很少出现的大号
                for num in [10, 11, 12]:
                    if pos1_counter.get(num, 0) <= 2:  # 50期内出现≤2次
                        position_kills.append(num)
            
            # 位置2杀号：很少出现小号
            if position_data[2]:
                pos2_counter = Counter(position_data[2])
                # 杀掉在位置2很少出现的小号
                for num in [1, 2, 3]:
                    if pos2_counter.get(num, 0) <= 2:  # 50期内出现≤2次
                        position_kills.append(num)
            
            return list(set(position_kills))[:4]  # 最多4个
            
        except:
            return [11, 12, 1]  # 备用
    
    def _get_frequency_based_kills(self) -> List[int]:
        """基于频率分析的杀号"""
        try:
            # 分析最近30期的频率
            all_blues = []
            for i, row in self.data.head(30).iterrows():
                from test_kill_algorithm import parse_numbers
                _, blue_balls = parse_numbers(row)
                
                if isinstance(blue_balls, list):
                    all_blues.extend(blue_balls)
                else:
                    all_blues.append(blue_balls)
            
            blue_counter = Counter(all_blues)
            
            # 选择频率最低的号码
            sorted_by_freq = sorted(blue_counter.items(), key=lambda x: x[1])
            frequency_kills = [num for num, freq in sorted_by_freq[:5]]
            
            return frequency_kills
            
        except:
            return [5, 9, 11, 12]  # 备用冷号
    
    def _get_trend_based_kills(self, recent_periods: List[List[int]]) -> List[int]:
        """基于近期趋势的杀号"""
        try:
            # 分析最近3期的趋势
            recent_blues = []
            for period in recent_periods[:3]:
                if isinstance(period, list):
                    recent_blues.extend(period)
                else:
                    recent_blues.append(period)
            
            recent_counter = Counter(recent_blues)
            
            # 找出最近没有出现的号码
            all_numbers = set(range(1, 13))
            recent_appeared = set(recent_blues)
            not_appeared = all_numbers - recent_appeared
            
            # 优先杀掉最近没有出现的号码
            trend_kills = list(not_appeared)
            
            # 如果不够，添加最近出现频率最低的
            if len(trend_kills) < 4:
                sorted_by_recent = sorted(recent_counter.items(), key=lambda x: x[1])
                for num, freq in sorted_by_recent:
                    if num not in trend_kills and len(trend_kills) < 4:
                        trend_kills.append(num)
            
            return trend_kills[:4]
            
        except:
            return [5, 9, 11, 12]  # 备用
    
    def _get_recent_active_numbers(self, recent_periods: List[List[int]]) -> Set[int]:
        """获取近期活跃号码"""
        active_numbers = set()
        
        # 前2期出现的号码都认为是活跃的
        for period in recent_periods[:2]:
            if isinstance(period, list):
                active_numbers.update(period)
            else:
                active_numbers.add(period)
        
        return active_numbers
    
    def _apply_smart_filters(self, kill_scores: Dict[int, float], recent_active: Set[int]) -> Dict[int, float]:
        """应用智能过滤规则"""
        filtered_scores = {}
        
        for num, score in kill_scores.items():
            # 基本范围检查
            if not (1 <= num <= 12):
                continue
            
            # 避免杀掉危险号码（经常被误杀）
            if num in self.dangerous_numbers:
                score *= 0.1  # 大幅降权
            
            # 避免杀掉热号
            if num in self.hot_numbers:
                score *= 0.3  # 降权
            
            # 避免杀掉近期活跃号码
            if num in recent_active:
                score *= 0.2  # 降权
            
            filtered_scores[num] = score
        
        return filtered_scores
    
    def test_optimization_effect(self):
        """测试优化效果"""
        print("\n🧪 测试优化效果...")
        
        test_cases = [
            {
                'name': '测试案例1',
                'recent_periods': [[4, 10], [1, 6], [3, 6]],
                'expected_improvement': '避免杀掉4, 6等活跃号码'
            },
            {
                'name': '测试案例2', 
                'recent_periods': [[7, 10], [1, 8], [4, 9]],
                'expected_improvement': '基于位置和频率分析'
            }
        ]
        
        for case in test_cases:
            print(f"\n  {case['name']}:")
            print(f"    近期蓝球: {case['recent_periods']}")
            
            optimized_kills = self.calculate_optimized_blue_kills(case['recent_periods'], 3)
            
            # 检查是否避免了危险号码
            dangerous_in_kills = [num for num in optimized_kills if num in self.dangerous_numbers]
            hot_in_kills = [num for num in optimized_kills if num in self.hot_numbers]
            
            print(f"    优化杀号: {optimized_kills}")
            print(f"    危险号码检查: {'✅ 避免' if not dangerous_in_kills else f'⚠️ 包含{dangerous_in_kills}'}")
            print(f"    热号检查: {'✅ 避免' if not hot_in_kills else f'⚠️ 包含{hot_in_kills}'}")
            print(f"    预期改进: {case['expected_improvement']}")

def test_optimized_blue_killer():
    """测试优化的蓝球杀号算法"""
    print("🔵 测试优化的蓝球杀号算法")
    print("=" * 60)
    
    # 加载数据
    try:
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}: {len(data)} 期")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 初始化优化算法
        optimized_killer = OptimizedBlueBallKiller(data)
        
        # 测试优化效果
        optimized_killer.test_optimization_effect()
        
        # 获取最新数据进行测试
        recent_periods = []
        for i in range(4):
            if i < len(data):
                from test_kill_algorithm import parse_numbers
                _, blue_balls = parse_numbers(data.iloc[i])
                recent_periods.append(blue_balls)
        
        print(f"\n📊 使用最新数据测试:")
        print(f"最近蓝球: {recent_periods[:3]}")
        
        optimized_kills = optimized_killer.calculate_optimized_blue_kills(recent_periods, 3)
        
        print(f"\n🎯 优化算法总结:")
        print(f"  杀号数量: {len(optimized_kills)}个 (从5个减少到3个)")
        print(f"  杀号列表: {sorted(optimized_kills)}")
        
        if optimized_kills:
            odd_count = sum(1 for num in optimized_kills if num % 2 == 1)
            small_count = sum(1 for num in optimized_kills if num <= 6)
            print(f"  特征分析: 奇数{odd_count}个, 偶数{len(optimized_kills)-odd_count}个")
            print(f"  大小分布: 小数{small_count}个, 大数{len(optimized_kills)-small_count}个")
            print(f"  号码范围: {min(optimized_kills)}-{max(optimized_kills)}")
        
        print(f"\n✅ 优化算法测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_optimized_blue_killer()
