"""
杀号模块
实现候选号码排除策略，提高预测准确性
"""

import numpy as np
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import parse_numbers, calculate_success_rate


class NumberKiller:
    """号码杀号器"""
    
    def __init__(self):
        """初始化杀号器"""
        self.red_kill_history = []  # 红球杀号历史
        self.blue_kill_history = []  # 蓝球杀号历史
        self.red_success_history = []  # 红球杀号成功历史
        self.blue_success_history = []  # 蓝球杀号成功历史
    
    def analyze_number_patterns(self, data, max_periods: int = 100) -> Dict:
        """
        分析号码出现模式
        
        Args:
            data: 历史数据DataFrame
            max_periods: 分析的最大期数
            
        Returns:
            Dict: 分析结果
        """
        analysis_data = data.head(max_periods) if len(data) > max_periods else data
        
        red_numbers = []
        blue_numbers = []
        
        for _, row in analysis_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_numbers.extend(red_balls)
            blue_numbers.extend(blue_balls)
        
        # 统计频率
        red_freq = Counter(red_numbers)
        blue_freq = Counter(blue_numbers)
        
        # 计算期望频率
        total_periods = len(analysis_data)
        red_expected_freq = total_periods * 5 / 35  # 每个红球的期望出现次数
        blue_expected_freq = total_periods * 2 / 12  # 每个蓝球的期望出现次数
        
        return {
            'red_freq': red_freq,
            'blue_freq': blue_freq,
            'red_expected': red_expected_freq,
            'blue_expected': blue_expected_freq,
            'total_periods': total_periods
        }
    
    def calculate_cold_numbers(self, data, recent_periods: int = 20) -> Tuple[List[int], List[int]]:
        """
        计算冷号（最近期数内出现频率低的号码）
        
        Args:
            data: 历史数据DataFrame
            recent_periods: 最近期数
            
        Returns:
            Tuple[List[int], List[int]]: (红球冷号, 蓝球冷号)
        """
        recent_data = data.head(recent_periods) if len(data) > recent_periods else data
        
        red_numbers = []
        blue_numbers = []
        
        for _, row in recent_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_numbers.extend(red_balls)
            blue_numbers.extend(blue_balls)
        
        # 统计频率
        red_freq = Counter(red_numbers)
        blue_freq = Counter(blue_numbers)
        
        # 找出冷号（出现频率最低的号码）
        all_red = list(range(1, 36))
        all_blue = list(range(1, 13))
        
        red_cold = sorted(all_red, key=lambda x: red_freq.get(x, 0))
        blue_cold = sorted(all_blue, key=lambda x: blue_freq.get(x, 0))
        
        return red_cold, blue_cold
    
    def calculate_consecutive_absence(self, data) -> Tuple[Dict[int, int], Dict[int, int]]:
        """
        计算号码连续未出现的期数
        
        Args:
            data: 历史数据DataFrame
            
        Returns:
            Tuple[Dict[int, int], Dict[int, int]]: (红球连续未出现期数, 蓝球连续未出现期数)
        """
        red_absence = {i: 0 for i in range(1, 36)}
        blue_absence = {i: 0 for i in range(1, 13)}
        
        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            
            # 更新连续未出现期数
            for num in range(1, 36):
                if num in red_balls:
                    red_absence[num] = 0
                else:
                    red_absence[num] += 1
            
            for num in range(1, 13):
                if num in blue_balls:
                    blue_absence[num] = 0
                else:
                    blue_absence[num] += 1
        
        return red_absence, blue_absence
    
    def predict_kill_numbers(self, data, position: int, ball_type: str = 'red') -> List[int]:
        """
        预测要杀掉的号码
        
        Args:
            data: 历史数据DataFrame
            position: 位置（1-5为红球位置，1-2为蓝球位置）
            ball_type: 球类型 ('red' 或 'blue')
            
        Returns:
            List[int]: 要杀掉的号码列表（2个）
        """
        if ball_type == 'red':
            return self._predict_red_kill_numbers(data, position)
        else:
            return self._predict_blue_kill_numbers(data, position)
    
    def _predict_red_kill_numbers(self, data, position: int) -> List[int]:
        """
        预测红球杀号
        
        Args:
            data: 历史数据DataFrame
            position: 红球位置 (1-5)
            
        Returns:
            List[int]: 要杀掉的红球号码列表
        """
        # 分析该位置的历史出现情况
        position_numbers = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            sorted_red = sorted(red_balls)
            if position <= len(sorted_red):
                position_numbers.append(sorted_red[position - 1])
        
        # 统计频率
        freq = Counter(position_numbers)
        
        # 计算冷号和连续未出现
        cold_numbers, _ = self.calculate_cold_numbers(data, recent_periods=30)
        red_absence, _ = self.calculate_consecutive_absence(data)
        
        # 综合评分：频率低 + 冷号 + 连续未出现期数长
        scores = {}
        for num in range(1, 36):
            frequency_score = 1.0 / (freq.get(num, 0) + 1)  # 频率越低分数越高
            cold_score = 1.0 if num in cold_numbers[:10] else 0.0  # 是否为冷号
            absence_score = red_absence.get(num, 0) / 50.0  # 连续未出现期数归一化
            
            scores[num] = frequency_score + cold_score + absence_score
        
        # 选择分数最高的2个号码作为杀号
        kill_candidates = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)
        
        # 确保选择的号码具有一定的差异性
        selected = []
        for num in kill_candidates:
            if len(selected) >= 2:
                break
            if not selected or abs(num - selected[-1]) >= 3:
                selected.append(num)
        
        # 如果选择不足2个，补充剩余的
        while len(selected) < 2 and len(selected) < len(kill_candidates):
            for num in kill_candidates:
                if num not in selected:
                    selected.append(num)
                    break
        
        return selected[:2]
    
    def _predict_blue_kill_numbers(self, data, position: int) -> List[int]:
        """
        预测蓝球杀号
        
        Args:
            data: 历史数据DataFrame
            position: 蓝球位置 (1-2)
            
        Returns:
            List[int]: 要杀掉的蓝球号码列表
        """
        # 分析该位置的历史出现情况
        position_numbers = []
        for _, row in data.iterrows():
            _, blue_balls = parse_numbers(row)
            sorted_blue = sorted(blue_balls)
            if position <= len(sorted_blue):
                position_numbers.append(sorted_blue[position - 1])
        
        # 统计频率
        freq = Counter(position_numbers)
        
        # 计算冷号和连续未出现
        _, cold_numbers = self.calculate_cold_numbers(data, recent_periods=30)
        _, blue_absence = self.calculate_consecutive_absence(data)
        
        # 综合评分
        scores = {}
        for num in range(1, 13):
            frequency_score = 1.0 / (freq.get(num, 0) + 1)
            cold_score = 1.0 if num in cold_numbers[:5] else 0.0
            absence_score = blue_absence.get(num, 0) / 30.0
            
            scores[num] = frequency_score + cold_score + absence_score
        
        # 选择分数最高的2个号码
        kill_candidates = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)
        return kill_candidates[:2]
    
    def validate_kill_accuracy(self, data, start_index: int, end_index: int) -> float:
        """
        验证杀号准确率
        
        Args:
            data: 历史数据DataFrame
            start_index: 开始索引
            end_index: 结束索引
            
        Returns:
            float: 杀号成功率
        """
        success_count = 0
        total_count = 0
        
        for i in range(start_index, min(end_index, len(data) - 1)):
            # 使用第i+1期之后的数据预测第i期
            train_data = data.iloc[i + 1:]
            actual_red, actual_blue = parse_numbers(data.iloc[i])
            
            # 预测杀号
            all_kills_correct = True
            
            # 检查红球杀号
            for pos in range(1, 6):
                kill_numbers = self.predict_kill_numbers(train_data, pos, 'red')
                if any(num in actual_red for num in kill_numbers):
                    all_kills_correct = False
                    break
            
            # 检查蓝球杀号
            if all_kills_correct:
                for pos in range(1, 3):
                    kill_numbers = self.predict_kill_numbers(train_data, pos, 'blue')
                    if any(num in actual_blue for num in kill_numbers):
                        all_kills_correct = False
                        break
            
            if all_kills_correct:
                success_count += 1
            total_count += 1
        
        return success_count / total_count if total_count > 0 else 0.0
