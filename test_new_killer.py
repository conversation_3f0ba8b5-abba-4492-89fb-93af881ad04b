#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的贝叶斯+马尔科夫链杀号系统
"""

from src.systems.main import LotteryPredictor
import pandas as pd

def test_new_killer():
    """测试新的杀号系统"""
    print("🎯 测试贝叶斯+马尔科夫链杀号系统")
    print("=" * 60)
    
    # 初始化预测系统
    predictor = LotteryPredictor()
    
    # 测试前5期的预测
    for i in range(5):
        print(f"\n📅 测试第 {i+1} 期预测:")
        
        try:
            # 进行预测
            prediction = predictor.predict_next_period(i)
            
            # 显示杀号结果
            kill_numbers = prediction['kill_numbers']
            
            print(f"  期号: {prediction['period']}")
            print(f"  红球杀号: {kill_numbers['red_universal']} (共{len(kill_numbers['red_universal'])}个)")
            print(f"  蓝球杀号: {kill_numbers['blue_universal']} (共{len(kill_numbers['blue_universal'])}个)")
            print(f"  杀号成功率: {prediction['kill_success_rate']:.1%}")
            
            # 显示预测的号码组合
            enhanced_selection = prediction['enhanced_selection']
            print(f"  推荐号码: 红球{enhanced_selection[0]} 蓝球{enhanced_selection[1]}")
            
        except Exception as e:
            print(f"  ❌ 预测失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    test_new_killer()
