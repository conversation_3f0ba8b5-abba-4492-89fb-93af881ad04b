"""
增强号码选择器 - 解决规则命中但号码不准的问题
基于更细粒度的规则和号码间相关性分析
"""

import numpy as np
from typing import List, Tuple, Dict, Set
from collections import Counter
import itertools


class EnhancedNumberSelector:
    """增强号码选择器"""
    
    def __init__(self):
        self.historical_data = []
        self.position_patterns = {}
        self.number_correlations = {}
        self.sum_patterns = {}
        
    def initialize(self, historical_data: List[Tuple[List[int], List[int]]]):
        """初始化选择器"""
        self.historical_data = historical_data
        self._analyze_position_patterns()
        self._analyze_number_correlations()
        self._analyze_sum_patterns()
        
    def _analyze_position_patterns(self):
        """分析位置模式"""
        # 分析每个位置的号码分布特征
        for pos in range(1, 6):  # 红球5个位置
            position_numbers = []
            for red_balls, _ in self.historical_data:
                sorted_red = sorted(red_balls)
                if pos <= len(sorted_red):
                    position_numbers.append(sorted_red[pos-1])
            
            self.position_patterns[f'red_{pos}'] = {
                'numbers': position_numbers,
                'frequency': Counter(position_numbers),
                'avg': np.mean(position_numbers) if position_numbers else 0,
                'std': np.std(position_numbers) if position_numbers else 0,
                'range': (min(position_numbers), max(position_numbers)) if position_numbers else (1, 35)
            }
        
        # 分析蓝球位置
        for pos in range(1, 3):  # 蓝球2个位置
            position_numbers = []
            for _, blue_balls in self.historical_data:
                sorted_blue = sorted(blue_balls)
                if pos <= len(sorted_blue):
                    position_numbers.append(sorted_blue[pos-1])
            
            self.position_patterns[f'blue_{pos}'] = {
                'numbers': position_numbers,
                'frequency': Counter(position_numbers),
                'avg': np.mean(position_numbers) if position_numbers else 0,
                'std': np.std(position_numbers) if position_numbers else 0,
                'range': (min(position_numbers), max(position_numbers)) if position_numbers else (1, 12)
            }
    
    def _analyze_number_correlations(self):
        """分析号码间相关性"""
        # 分析相邻位置的号码关系
        for i in range(1, 5):  # 位置1-4与位置2-5的关系
            correlations = []
            for red_balls, _ in self.historical_data:
                sorted_red = sorted(red_balls)
                if len(sorted_red) >= i+1:
                    diff = sorted_red[i] - sorted_red[i-1]  # 相邻位置差值
                    correlations.append(diff)
            
            self.number_correlations[f'red_diff_{i}_{i+1}'] = {
                'diffs': correlations,
                'avg_diff': np.mean(correlations) if correlations else 0,
                'std_diff': np.std(correlations) if correlations else 0
            }
    
    def _analyze_sum_patterns(self):
        """分析和值模式"""
        red_sums = []
        blue_sums = []
        
        for red_balls, blue_balls in self.historical_data:
            red_sums.append(sum(red_balls))
            blue_sums.append(sum(blue_balls))
        
        self.sum_patterns = {
            'red_sum': {
                'values': red_sums,
                'avg': np.mean(red_sums) if red_sums else 0,
                'std': np.std(red_sums) if red_sums else 0,
                'range': (min(red_sums), max(red_sums)) if red_sums else (15, 175)
            },
            'blue_sum': {
                'values': blue_sums,
                'avg': np.mean(blue_sums) if blue_sums else 0,
                'std': np.std(blue_sums) if blue_sums else 0,
                'range': (min(blue_sums), max(blue_sums)) if blue_sums else (3, 23)
            }
        }
    
    def select_enhanced_numbers(self, 
                              odd_even_state: str,
                              size_state: str, 
                              blue_size_state: str,
                              kill_numbers: Dict,
                              seed: int = None) -> Tuple[List[int], List[int]]:
        """
        基于增强规则选择号码
        
        Args:
            odd_even_state: 奇偶比状态 (如 "2:3")
            size_state: 大小比状态 (如 "2:3") 
            blue_size_state: 蓝球大小比状态 (如 "1:1")
            kill_numbers: 杀号信息
            seed: 随机种子
            
        Returns:
            Tuple[List[int], List[int]]: (红球, 蓝球)
        """
        if seed:
            np.random.seed(seed)
        
        # 1. 基于规则生成候选号码池
        red_candidates = self._generate_red_candidates(odd_even_state, size_state, kill_numbers)
        blue_candidates = self._generate_blue_candidates(blue_size_state, kill_numbers)
        
        # 2. 基于位置模式筛选
        red_position_candidates = self._filter_by_position_patterns(red_candidates, 'red')
        blue_position_candidates = self._filter_by_position_patterns(blue_candidates, 'blue')
        
        # 3. 基于相关性约束选择
        selected_red = self._select_with_correlations(red_position_candidates, odd_even_state, size_state)
        selected_blue = self._select_with_correlations(blue_position_candidates, blue_size_state, None)
        
        return selected_red, selected_blue
    
    def _generate_red_candidates(self, odd_even_state: str, size_state: str, kill_numbers: Dict) -> Dict[int, List[int]]:
        """为每个红球位置生成候选号码"""
        odd_count, even_count = map(int, odd_even_state.split(':'))
        small_count, big_count = map(int, size_state.split(':'))
        
        position_candidates = {}
        
        for pos in range(1, 6):
            candidates = []
            
            # 基础候选池
            pos_pattern = self.position_patterns.get(f'red_{pos}', {})
            pos_range = pos_pattern.get('range', (1, 35))
            pos_avg = pos_pattern.get('avg', 18)
            
            # 根据位置特征确定候选范围
            if pos == 1:  # 第1位通常较小
                candidate_range = range(max(1, int(pos_avg - 10)), min(25, int(pos_avg + 5)))
            elif pos == 5:  # 第5位通常较大
                candidate_range = range(max(15, int(pos_avg - 5)), min(36, int(pos_avg + 10)))
            else:  # 中间位置
                candidate_range = range(max(1, int(pos_avg - 8)), min(36, int(pos_avg + 8)))
            
            # 应用杀号
            red_kills = kill_numbers.get('red', [])
            pos_kills = red_kills[pos-1] if pos-1 < len(red_kills) else []
            
            for num in candidate_range:
                if num not in pos_kills:
                    candidates.append(num)
            
            position_candidates[pos] = candidates
        
        return position_candidates
    
    def _generate_blue_candidates(self, blue_size_state: str, kill_numbers: Dict) -> Dict[int, List[int]]:
        """为每个蓝球位置生成候选号码"""
        small_count, big_count = map(int, blue_size_state.split(':'))
        
        position_candidates = {}
        
        for pos in range(1, 3):
            candidates = []
            
            # 基础候选池
            pos_pattern = self.position_patterns.get(f'blue_{pos}', {})
            pos_avg = pos_pattern.get('avg', 6.5)
            
            # 根据位置和大小比要求确定候选范围
            if pos == 1:  # 蓝球第1位通常较小
                if small_count > 0:
                    candidate_range = range(1, 7)  # 小号优先
                else:
                    candidate_range = range(7, 13)  # 大号
            else:  # 蓝球第2位
                if big_count > 0:
                    candidate_range = range(7, 13)  # 大号优先
                else:
                    candidate_range = range(1, 7)  # 小号
            
            # 应用杀号
            blue_kills = kill_numbers.get('blue', [])
            pos_kills = blue_kills[pos-1] if pos-1 < len(blue_kills) else []
            
            for num in candidate_range:
                if num not in pos_kills:
                    candidates.append(num)
            
            position_candidates[pos] = candidates
        
        return position_candidates
    
    def _filter_by_position_patterns(self, position_candidates: Dict[int, List[int]], ball_type: str) -> Dict[int, List[int]]:
        """基于位置模式进一步筛选候选号码"""
        filtered_candidates = {}
        
        for pos, candidates in position_candidates.items():
            pattern_key = f'{ball_type}_{pos}'
            pos_pattern = self.position_patterns.get(pattern_key, {})
            
            if not pos_pattern:
                filtered_candidates[pos] = candidates
                continue
            
            # 基于历史频率和统计特征筛选
            pos_freq = pos_pattern.get('frequency', Counter())
            pos_avg = pos_pattern.get('avg', 0)
            pos_std = pos_pattern.get('std', 0)
            
            scored_candidates = []
            for num in candidates:
                score = 0
                
                # 频率得分
                freq_score = pos_freq.get(num, 0) / max(1, len(self.historical_data))
                score += freq_score * 0.4
                
                # 统计特征得分 (接近平均值的得分更高)
                if pos_std > 0:
                    stat_score = 1 - min(1, abs(num - pos_avg) / (2 * pos_std))
                    score += stat_score * 0.6
                
                scored_candidates.append((num, score))
            
            # 选择得分较高的候选号码
            scored_candidates.sort(key=lambda x: x[1], reverse=True)
            top_candidates = [num for num, score in scored_candidates[:min(10, len(scored_candidates))]]
            
            filtered_candidates[pos] = top_candidates
        
        return filtered_candidates
    
    def _select_with_correlations(self, position_candidates: Dict[int, List[int]], 
                                state: str, size_state: str = None) -> List[int]:
        """基于相关性约束选择最终号码"""
        if not position_candidates:
            return []
        
        # 生成所有可能的组合
        positions = sorted(position_candidates.keys())
        candidate_lists = [position_candidates[pos] for pos in positions]
        
        if not all(candidate_lists):
            return []
        
        # 限制组合数量以避免计算爆炸
        max_combinations = 1000
        all_combinations = list(itertools.product(*candidate_lists))
        
        if len(all_combinations) > max_combinations:
            # 随机采样
            selected_indices = np.random.choice(len(all_combinations), max_combinations, replace=False)
            all_combinations = [all_combinations[i] for i in selected_indices]
        
        # 评估每个组合
        best_combination = None
        best_score = -1
        
        for combination in all_combinations:
            score = self._evaluate_combination(combination, state, size_state)
            if score > best_score:
                best_score = score
                best_combination = combination
        
        return list(best_combination) if best_combination else []
    
    def _evaluate_combination(self, combination: Tuple[int, ...], state: str, size_state: str = None) -> float:
        """
        多维度联合评估号码组合质量
        增加更严格的约束条件以提高精准度
        """
        if not combination:
            return 0

        score = 0
        combination_list = list(combination)
        sorted_combination = sorted(combination_list)

        # 1. 基础状态匹配得分（必须完全匹配）
        if len(combination_list) == 5:  # 红球
            # 奇偶比严格匹配
            odd_count = sum(1 for x in combination_list if x % 2 == 1)
            even_count = len(combination_list) - odd_count
            expected_odd, expected_even = map(int, state.split(':'))

            if odd_count == expected_odd and even_count == expected_even:
                score += 50  # 提高权重
            else:
                return 0  # 不匹配直接淘汰

            # 大小比严格匹配
            if size_state:
                small_count = sum(1 for x in combination_list if x <= 18)
                big_count = len(combination_list) - small_count
                expected_small, expected_big = map(int, size_state.split(':'))

                if small_count == expected_small and big_count == expected_big:
                    score += 50  # 提高权重
                else:
                    return 0  # 不匹配直接淘汰

        elif len(combination_list) == 2:  # 蓝球
            small_count = sum(1 for x in combination_list if x <= 6)
            big_count = len(combination_list) - small_count
            expected_small, expected_big = map(int, state.split(':'))

            if small_count == expected_small and big_count == expected_big:
                score += 60
            else:
                return 0  # 不匹配直接淘汰

        # 2. 和值约束（严格范围）
        combination_sum = sum(combination_list)
        if len(combination_list) == 5:  # 红球
            sum_pattern = self.sum_patterns.get('red_sum', {})
            expected_sum = sum_pattern.get('avg', 90)
            sum_std = sum_pattern.get('std', 20)
            sum_range = sum_pattern.get('range', (60, 140))

            # 和值必须在合理范围内
            if combination_sum < sum_range[0] or combination_sum > sum_range[1]:
                return 0  # 超出范围直接淘汰

            # 和值越接近历史平均值得分越高
            if sum_std > 0:
                sum_score = max(0, 30 - abs(combination_sum - expected_sum) / sum_std * 10)
                score += sum_score

        # 3. 跨度约束
        if len(sorted_combination) >= 2:
            span = sorted_combination[-1] - sorted_combination[0]
            if len(combination_list) == 5:  # 红球跨度通常15-25
                if span < 10 or span > 30:
                    score -= 20  # 跨度异常扣分
                elif 15 <= span <= 25:
                    score += 20  # 合理跨度加分
            else:  # 蓝球跨度通常3-8
                if span < 1 or span > 10:
                    score -= 15
                elif 3 <= span <= 8:
                    score += 15

        # 4. 连号约束
        consecutive_count = 0
        for i in range(len(sorted_combination) - 1):
            if sorted_combination[i+1] - sorted_combination[i] == 1:
                consecutive_count += 1

        if consecutive_count >= 3:  # 3个以上连号很少见
            score -= 30
        elif consecutive_count == 1 or consecutive_count == 2:  # 1-2个连号较常见
            score += 10

        # 5. 位置合理性得分（更严格）
        for i, num in enumerate(sorted_combination):
            pos = i + 1
            ball_type = 'red' if len(combination_list) == 5 else 'blue'
            pattern_key = f'{ball_type}_{pos}'
            pos_pattern = self.position_patterns.get(pattern_key, {})

            if pos_pattern:
                pos_range = pos_pattern.get('range', (1, 35))
                pos_avg = pos_pattern.get('avg', 0)
                pos_std = pos_pattern.get('std', 1)

                # 号码必须在该位置的历史范围内
                if num < pos_range[0] or num > pos_range[1]:
                    score -= 15  # 超出位置范围扣分

                # 越接近位置平均值得分越高
                if pos_std > 0:
                    pos_score = max(0, 15 - abs(num - pos_avg) / pos_std * 3)
                    score += pos_score

        # 6. AC值约束（号码分布离散度）
        if len(combination_list) == 5:
            ac_value = self._calculate_ac_value(sorted_combination)
            if ac_value < 3 or ac_value > 8:  # AC值异常
                score -= 20
            elif 4 <= ac_value <= 6:  # AC值正常
                score += 15

        # 7. 质数约束
        primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31]
        prime_count = sum(1 for x in combination_list if x in primes)
        if len(combination_list) == 5:
            if prime_count >= 4:  # 质数过多
                score -= 10
            elif 1 <= prime_count <= 2:  # 质数适中
                score += 10

        return max(0, score)  # 确保得分非负

    def _calculate_ac_value(self, sorted_numbers: List[int]) -> int:
        """计算AC值（号码分布的离散程度）"""
        if len(sorted_numbers) < 2:
            return 0

        differences = set()
        for i in range(len(sorted_numbers)):
            for j in range(i + 1, len(sorted_numbers)):
                differences.add(abs(sorted_numbers[i] - sorted_numbers[j]))

        return len(differences) - len(sorted_numbers) + 1
