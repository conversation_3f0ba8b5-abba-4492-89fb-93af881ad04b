{"current_weights": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "current_confidences": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}, "evidence_performance": {"markov_chain": {"hits": 120, "total": 1349, "accuracy": 0.08895478131949593}, "frequency_analysis": {"hits": 120, "total": 1349, "accuracy": 0.08895478131949593}, "trend_following": {"hits": 120, "total": 1349, "accuracy": 0.08895478131949593}, "pattern_recognition": {"hits": 120, "total": 1349, "accuracy": 0.08895478131949593}}, "prediction_history": [{"period": "25068", "predicted_state": ["2:3", 0.44884400944266784], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44884400944266784], "frequency_analysis": ["2:3", 0.44884400944266784], "trend_following": ["2:3", 0.44884400944266784], "pattern_recognition": ["2:3", 0.44884400944266784]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25067", "predicted_state": ["2:3", 0.4537038916578267], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4537038916578267], "frequency_analysis": ["2:3", 0.4537038916578267], "trend_following": ["2:3", 0.4537038916578267], "pattern_recognition": ["2:3", 0.4537038916578267]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25066", "predicted_state": ["2:3", 0.42184691152537834], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.42184691152537834], "frequency_analysis": ["2:3", 0.42184691152537834], "trend_following": ["2:3", 0.42184691152537834], "pattern_recognition": ["2:3", 0.42184691152537834]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25065", "predicted_state": ["2:3", 0.44859555508756177], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44859555508756177], "frequency_analysis": ["2:3", 0.44859555508756177], "trend_following": ["2:3", 0.44859555508756177], "pattern_recognition": ["2:3", 0.44859555508756177]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25064", "predicted_state": ["2:3", 0.4588940828607134], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4588940828607134], "frequency_analysis": ["2:3", 0.4588940828607134], "trend_following": ["2:3", 0.4588940828607134], "pattern_recognition": ["2:3", 0.4588940828607134]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25063", "predicted_state": ["3:2", 0.47966750309049533], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.47966750309049533], "frequency_analysis": ["3:2", 0.47966750309049533], "trend_following": ["3:2", 0.47966750309049533], "pattern_recognition": ["3:2", 0.47966750309049533]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25062", "predicted_state": ["3:2", 0.5059654692411076], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5059654692411076], "frequency_analysis": ["3:2", 0.5059654692411076], "trend_following": ["3:2", 0.5059654692411076], "pattern_recognition": ["3:2", 0.5059654692411076]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25061", "predicted_state": ["3:2", 0.5383673044302274], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5383673044302274], "frequency_analysis": ["3:2", 0.5383673044302274], "trend_following": ["3:2", 0.5383673044302274], "pattern_recognition": ["3:2", 0.5383673044302274]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25060", "predicted_state": ["3:2", 0.5664117208211358], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5664117208211358], "frequency_analysis": ["3:2", 0.5664117208211358], "trend_following": ["3:2", 0.5664117208211358], "pattern_recognition": ["3:2", 0.5664117208211358]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25059", "predicted_state": ["3:2", 0.5332402208882324], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5332402208882324], "frequency_analysis": ["3:2", 0.5332402208882324], "trend_following": ["3:2", 0.5332402208882324], "pattern_recognition": ["3:2", 0.5332402208882324]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25068", "predicted_state": ["2:3", 0.44884400944266784], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44884400944266784], "frequency_analysis": ["2:3", 0.44884400944266784], "trend_following": ["2:3", 0.44884400944266784], "pattern_recognition": ["2:3", 0.44884400944266784]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25067", "predicted_state": ["2:3", 0.4537038916578267], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4537038916578267], "frequency_analysis": ["2:3", 0.4537038916578267], "trend_following": ["2:3", 0.4537038916578267], "pattern_recognition": ["2:3", 0.4537038916578267]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25066", "predicted_state": ["2:3", 0.42184691152537834], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.42184691152537834], "frequency_analysis": ["2:3", 0.42184691152537834], "trend_following": ["2:3", 0.42184691152537834], "pattern_recognition": ["2:3", 0.42184691152537834]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25065", "predicted_state": ["2:3", 0.44859555508756177], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44859555508756177], "frequency_analysis": ["2:3", 0.44859555508756177], "trend_following": ["2:3", 0.44859555508756177], "pattern_recognition": ["2:3", 0.44859555508756177]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25064", "predicted_state": ["2:3", 0.4588940828607134], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4588940828607134], "frequency_analysis": ["2:3", 0.4588940828607134], "trend_following": ["2:3", 0.4588940828607134], "pattern_recognition": ["2:3", 0.4588940828607134]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25063", "predicted_state": ["3:2", 0.47966750309049533], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.47966750309049533], "frequency_analysis": ["3:2", 0.47966750309049533], "trend_following": ["3:2", 0.47966750309049533], "pattern_recognition": ["3:2", 0.47966750309049533]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25062", "predicted_state": ["3:2", 0.5059654692411076], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5059654692411076], "frequency_analysis": ["3:2", 0.5059654692411076], "trend_following": ["3:2", 0.5059654692411076], "pattern_recognition": ["3:2", 0.5059654692411076]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25061", "predicted_state": ["3:2", 0.5383673044302274], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5383673044302274], "frequency_analysis": ["3:2", 0.5383673044302274], "trend_following": ["3:2", 0.5383673044302274], "pattern_recognition": ["3:2", 0.5383673044302274]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25060", "predicted_state": ["3:2", 0.5664117208211358], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5664117208211358], "frequency_analysis": ["3:2", 0.5664117208211358], "trend_following": ["3:2", 0.5664117208211358], "pattern_recognition": ["3:2", 0.5664117208211358]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25059", "predicted_state": ["3:2", 0.5332402208882324], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5332402208882324], "frequency_analysis": ["3:2", 0.5332402208882324], "trend_following": ["3:2", 0.5332402208882324], "pattern_recognition": ["3:2", 0.5332402208882324]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}], "weight_momentum": {"markov_chain": -0.019999999999999976, "frequency_analysis": -0.019999999999999976, "trend_following": -0.019999999999999976, "pattern_recognition": -0.019999999999999976}, "confidence_momentum": {"markov_chain": -0.003999999999999997, "frequency_analysis": -0.003999999999999997, "trend_following": -0.003999999999999997, "pattern_recognition": -0.003999999999999997}}