{"current_weights": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "current_confidences": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}, "evidence_performance": {"markov_chain": {"hits": 120, "total": 1329, "accuracy": 0.09029345372460497}, "frequency_analysis": {"hits": 120, "total": 1329, "accuracy": 0.09029345372460497}, "trend_following": {"hits": 120, "total": 1329, "accuracy": 0.09029345372460497}, "pattern_recognition": {"hits": 120, "total": 1329, "accuracy": 0.09029345372460497}}, "prediction_history": [{"period": "25038", "predicted_state": ["2:3", 0.4884892037999577], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4884892037999577], "frequency_analysis": ["2:3", 0.4884892037999577], "trend_following": ["2:3", 0.4884892037999577], "pattern_recognition": ["2:3", 0.4884892037999577]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25037", "predicted_state": ["2:3", 0.502345545403799], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.502345545403799], "frequency_analysis": ["2:3", 0.502345545403799], "trend_following": ["2:3", 0.502345545403799], "pattern_recognition": ["2:3", 0.502345545403799]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25036", "predicted_state": ["2:3", 0.5272718541814672], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.5272718541814672], "frequency_analysis": ["2:3", 0.5272718541814672], "trend_following": ["2:3", 0.5272718541814672], "pattern_recognition": ["2:3", 0.5272718541814672]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25035", "predicted_state": ["2:3", 0.49900967638026544], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.49900967638026544], "frequency_analysis": ["2:3", 0.49900967638026544], "trend_following": ["2:3", 0.49900967638026544], "pattern_recognition": ["2:3", 0.49900967638026544]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25034", "predicted_state": ["2:3", 0.4682887187605946], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4682887187605946], "frequency_analysis": ["2:3", 0.4682887187605946], "trend_following": ["2:3", 0.4682887187605946], "pattern_recognition": ["2:3", 0.4682887187605946]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25033", "predicted_state": ["2:3", 0.42184123614733054], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.42184123614733054], "frequency_analysis": ["2:3", 0.42184123614733054], "trend_following": ["2:3", 0.42184123614733054], "pattern_recognition": ["2:3", 0.42184123614733054]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25032", "predicted_state": ["3:2", 0.4351984321878493], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4351984321878493], "frequency_analysis": ["3:2", 0.4351984321878493], "trend_following": ["3:2", 0.4351984321878493], "pattern_recognition": ["3:2", 0.4351984321878493]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25031", "predicted_state": ["3:2", 0.4608145175421331], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4608145175421331], "frequency_analysis": ["3:2", 0.4608145175421331], "trend_following": ["3:2", 0.4608145175421331], "pattern_recognition": ["3:2", 0.4608145175421331]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25030", "predicted_state": ["3:2", 0.4938399154334451], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4938399154334451], "frequency_analysis": ["3:2", 0.4938399154334451], "trend_following": ["3:2", 0.4938399154334451], "pattern_recognition": ["3:2", 0.4938399154334451]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25029", "predicted_state": ["3:2", 0.5628209491362746], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5628209491362746], "frequency_analysis": ["3:2", 0.5628209491362746], "trend_following": ["3:2", 0.5628209491362746], "pattern_recognition": ["3:2", 0.5628209491362746]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25028", "predicted_state": ["3:2", 0.5949953686134286], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5949953686134286], "frequency_analysis": ["3:2", 0.5949953686134286], "trend_following": ["3:2", 0.5949953686134286], "pattern_recognition": ["3:2", 0.5949953686134286]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25027", "predicted_state": ["3:2", 0.5547802562762755], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5547802562762755], "frequency_analysis": ["3:2", 0.5547802562762755], "trend_following": ["3:2", 0.5547802562762755], "pattern_recognition": ["3:2", 0.5547802562762755]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25026", "predicted_state": ["3:2", 0.5149699295737287], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5149699295737287], "frequency_analysis": ["3:2", 0.5149699295737287], "trend_following": ["3:2", 0.5149699295737287], "pattern_recognition": ["3:2", 0.5149699295737287]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25025", "predicted_state": ["3:2", 0.5548592238098974], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5548592238098974], "frequency_analysis": ["3:2", 0.5548592238098974], "trend_following": ["3:2", 0.5548592238098974], "pattern_recognition": ["3:2", 0.5548592238098974]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25024", "predicted_state": ["3:2", 0.538246636411502], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.538246636411502], "frequency_analysis": ["3:2", 0.538246636411502], "trend_following": ["3:2", 0.538246636411502], "pattern_recognition": ["3:2", 0.538246636411502]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25023", "predicted_state": ["3:2", 0.5329583808482097], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5329583808482097], "frequency_analysis": ["3:2", 0.5329583808482097], "trend_following": ["3:2", 0.5329583808482097], "pattern_recognition": ["3:2", 0.5329583808482097]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25022", "predicted_state": ["3:2", 0.5895022183139517], "actual_state": "5:0", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5895022183139517], "frequency_analysis": ["3:2", 0.5895022183139517], "trend_following": ["3:2", 0.5895022183139517], "pattern_recognition": ["3:2", 0.5895022183139517]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25021", "predicted_state": ["3:2", 0.6622779667620426], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.6622779667620426], "frequency_analysis": ["3:2", 0.6622779667620426], "trend_following": ["3:2", 0.6622779667620426], "pattern_recognition": ["3:2", 0.6622779667620426]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25020", "predicted_state": ["3:2", 0.5488034498352694], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5488034498352694], "frequency_analysis": ["3:2", 0.5488034498352694], "trend_following": ["3:2", 0.5488034498352694], "pattern_recognition": ["3:2", 0.5488034498352694]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25019", "predicted_state": ["3:2", 0.5107342252851066], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5107342252851066], "frequency_analysis": ["3:2", 0.5107342252851066], "trend_following": ["3:2", 0.5107342252851066], "pattern_recognition": ["3:2", 0.5107342252851066]}, "weights_used": {"markov_chain": 0.5898249423963289, "frequency_analysis": 0.31187090053761624, "trend_following": 0.049152078533027416, "pattern_recognition": 0.049152078533027416}, "confidences_used": {"markov_chain": 0.3, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}], "weight_momentum": {"markov_chain": -0.019999999999999976, "frequency_analysis": -0.019999999999999976, "trend_following": -0.019999999999999976, "pattern_recognition": -0.019999999999999976}, "confidence_momentum": {"markov_chain": -0.003999999999999997, "frequency_analysis": -0.003999999999999997, "trend_following": -0.003999999999999997, "pattern_recognition": -0.003999999999999997}}