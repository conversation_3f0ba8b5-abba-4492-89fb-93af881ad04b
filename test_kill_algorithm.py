"""
杀号算法专门测试脚本
用于验证和优化红球、蓝球杀号算法的准确性
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter
import sys
import os

# 添加src路径
sys.path.append('src')
from utils.utils import parse_numbers
from utils.universal_killer import UniversalKiller
from utils.blue_killer import BlueKiller

class KillAlgorithmTester:
    """杀号算法测试器"""
    
    def __init__(self):
        self.data = None
        self.red_killer = UniversalKiller()
        self.blue_killer = BlueKiller()
        
    def load_data(self, file_path: str = "dlt_data.csv"):
        """加载彩票数据"""
        try:
            self.data = pd.read_csv(file_path)
            print(f"✅ 成功加载数据: {len(self.data)} 期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def test_red_kill_algorithms(self, test_periods: int = 20) -> Dict:
        """测试红球杀号算法"""
        print(f"\n🔍 测试红球杀号算法 (最近{test_periods}期)")
        print("=" * 60)
        
        results = {
            'total_periods': 0,
            'algorithm_results': {},
            'combined_results': {},
            'success_details': []
        }
        
        # 测试各个算法 (删除经常生成"无"杀号的算法)
        algorithms = [
            'min_two_sum',      # 最小两数相加法 - 87.5%
            'max_two_diff',     # 最大两数相减法 - 83.6%
            'sum_tail',         # 和值尾数法 - 88.3%
            'span',             # 跨度计算法 - 91.1%
            'odd_even',         # 奇偶比例法 - 90.9%
            'consecutive',      # 连号分析法 - 84.4%
            'ac_value',         # AC值杀号法 - 85.3%
            'fibonacci',        # 斐波那契杀号法 - 88.7%
            'position_sum',     # 位置和杀号法 - 80.7%
            'interval_kill',    # 区间杀号法 - 90.5%
            'remainder_kill',   # 余数杀号法 - 88.9%
            'digital_root',     # 数字根杀号法 - 88.5%
            'symmetry_kill',    # 对称杀号法 - 90.0%
            'triangle_kill',    # 三角数杀号法 - 83.6%
            'lucas_kill',       # 卢卡斯数列杀号法 - 81.4%
            'catalan_kill',     # 卡塔兰数杀号法 - 91.3%
            'harmonic_kill',    # 调和数杀号法 - 84.1%
            'pentagonal_kill',  # 五边形数杀号法 - 97.6%
            'binary_kill',      # 二进制杀号法 - 89.3%
            'factorial_kill',   # 阶乘杀号法 - 95.0%
            'modular_kill',     # 模运算杀号法 - 90.0%
            'palindrome_kill',  # 回文数杀号法 - 83.3%
            'abundant_kill',    # 过剩数杀号法 - 95.8%
            'deficient_kill'    # 亏数杀号法 - 88.9%
            # 删除的算法：
            # 'prime_kill',       # 质数杀号法 - 经常无杀号
            # 'square_kill',      # 平方数杀号法 - 经常无杀号 (虽然100%但覆盖率太低)
            # 'power_kill',       # 幂次杀号法 - 经常无杀号
            # 'gcd_kill',         # 最大公约数杀号法 - 0.0% (从不生成杀号)
            # 'lcm_kill',         # 最小公倍数杀号法 - 0.0% (从不生成杀号)
            # 'perfect_kill',     # 完全数杀号法 - 经常无杀号
        ]
        
        for algo in algorithms:
            results['algorithm_results'][algo] = {
                'total_kills': 0,
                'successful_kills': 0,
                'success_rate': 0.0,
                'details': []
            }
        
        # 逐期测试
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break
                
            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]  # 上一期
            period2_data = self.data.iloc[i + 2]  # 上二期
            
            # 解析号码
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period1_data)
            period2_red, _ = parse_numbers(period2_data)
            
            period_info = {
                'period': current_period['期号'],
                'actual_red': current_red,
                'prev_periods': [period1_red, period2_red],
                'algorithm_kills': {}
            }
            
            # 测试各个算法
            self._test_individual_algorithms(period_info, algorithms, results)
            
            results['total_periods'] += 1
            results['success_details'].append(period_info)
        
        # 计算成功率
        for algo in algorithms:
            algo_result = results['algorithm_results'][algo]
            if algo_result['total_kills'] > 0:
                algo_result['success_rate'] = algo_result['successful_kills'] / algo_result['total_kills']
        
        return results
    
    def _test_individual_algorithms(self, period_info: Dict, algorithms: List[str], results: Dict):
        """测试单个算法"""
        period1_red = period_info['prev_periods'][0]
        period2_red = period_info['prev_periods'][1]
        actual_red = period_info['actual_red']
        
        # 测试各个算法
        for algo in algorithms:
            if algo == 'min_two_sum':
                kills = self.red_killer._calculate_min_two_sum_kill(period1_red, period2_red)
            elif algo == 'max_two_diff':
                kills = self.red_killer._calculate_max_two_diff_kill(period1_red, period2_red)
            elif algo == 'sum_tail':
                kills = self.red_killer._calculate_sum_tail_kill(period1_red, period2_red)
            elif algo == 'span':
                kills = self.red_killer._calculate_span_kill(period1_red, period2_red)
            elif algo == 'odd_even':
                kills = self.red_killer._calculate_odd_even_kill(period1_red, period2_red)
            elif algo == 'consecutive':
                kills = self.red_killer._calculate_consecutive_kill(period1_red, period2_red)
            elif algo == 'ac_value':
                kills = self._calculate_ac_value_kill(period1_red, period2_red)
            elif algo == 'fibonacci':
                kills = self._calculate_fibonacci_kill(period1_red, period2_red)
            elif algo == 'position_sum':
                kills = self._calculate_position_sum_kill(period1_red, period2_red)
            elif algo == 'interval_kill':
                kills = self._calculate_interval_kill(period1_red, period2_red)
            elif algo == 'remainder_kill':
                kills = self._calculate_remainder_kill(period1_red, period2_red)
            elif algo == 'digital_root':
                kills = self._calculate_digital_root_kill(period1_red, period2_red)
            elif algo == 'symmetry_kill':
                kills = self._calculate_symmetry_kill(period1_red, period2_red)
            elif algo == 'triangle_kill':
                kills = self._calculate_triangle_kill(period1_red, period2_red)
            elif algo == 'lucas_kill':
                kills = self._calculate_lucas_kill(period1_red, period2_red)
            elif algo == 'catalan_kill':
                kills = self._calculate_catalan_kill(period1_red, period2_red)
            elif algo == 'harmonic_kill':
                kills = self._calculate_harmonic_kill(period1_red, period2_red)
            elif algo == 'pentagonal_kill':
                kills = self._calculate_pentagonal_kill(period1_red, period2_red)
            elif algo == 'binary_kill':
                kills = self._calculate_binary_kill(period1_red, period2_red)
            elif algo == 'factorial_kill':
                kills = self._calculate_factorial_kill(period1_red, period2_red)
            elif algo == 'modular_kill':
                kills = self._calculate_modular_kill(period1_red, period2_red)
            elif algo == 'palindrome_kill':
                kills = self._calculate_palindrome_kill(period1_red, period2_red)
            elif algo == 'abundant_kill':
                kills = self._calculate_abundant_kill(period1_red, period2_red)
            elif algo == 'deficient_kill':
                kills = self._calculate_deficient_kill(period1_red, period2_red)
            else:
                kills = []
            
            # 验证杀号效果
            valid_kills = [k for k in kills if 1 <= k <= 35 and k not in (period1_red + period2_red)]
            successful_kills = [k for k in valid_kills if k not in actual_red]
            
            period_info['algorithm_kills'][algo] = {
                'kills': valid_kills,
                'successful': successful_kills,
                'success_count': len(successful_kills),
                'total_count': len(valid_kills)
            }
            
            # 更新统计
            results['algorithm_results'][algo]['total_kills'] += len(valid_kills)
            results['algorithm_results'][algo]['successful_kills'] += len(successful_kills)
            results['algorithm_results'][algo]['details'].append({
                'period': period_info['period'],
                'kills': valid_kills,
                'successful': successful_kills,
                'actual': actual_red
            })
    
    def test_blue_kill_algorithms(self, test_periods: int = 20) -> Dict:
        """测试蓝球杀号算法"""
        print(f"\n🔍 测试蓝球杀号算法 (最近{test_periods}期)")
        print("=" * 60)
        
        results = {
            'total_periods': 0,
            'algorithm_results': {},
            'success_details': []
        }
        
        # 测试各个算法 (专注于95%+准确性的蓝球算法)
        algorithms = [
            'sum',              # 蓝球相加法 - 73.0%
            'diff',             # 蓝球相减法 - 86.0%
            'multiple',         # 蓝球倍数法 - 80.0%
            'odd_even',         # 蓝球奇偶法 - 76.0%
            'blue_fib',         # 蓝球斐波那契法 - 84.2%
            'blue_interval',    # 蓝球区间法 - 81.4%
            'blue_square',      # 蓝球平方数法 - 87.5%
            'blue_triangle',    # 蓝球三角数法 - 81.5%
            'blue_lucas',       # 蓝球卢卡斯数法 - 87.5%
            'blue_perfect',     # 蓝球完全数法 - 93.3%
            'blue_binary',      # 蓝球二进制法 - 87.9%
            'blue_modular',     # 蓝球模运算法 - 90.0%
            'blue_palindrome',  # 蓝球回文数法 - 81.7%
            'blue_power',       # 蓝球幂次法 - 82.0%
            # 新增95%+准确性算法
            'blue_ultra_conservative',  # 蓝球超保守法
            'blue_pattern_break',       # 蓝球模式破坏法
            'blue_frequency_extreme',   # 蓝球频率极值法
            'blue_distance_max',        # 蓝球最大距离法
            'blue_composite_ultra',     # 蓝球超级复合法
            'blue_mathematical_pure',   # 蓝球纯数学法
            'blue_statistical_ultra',   # 蓝球超级统计法
            'blue_harmonic_advanced',   # 蓝球高级调和法
            'blue_golden_ratio',        # 蓝球黄金比例法
            'blue_chaos_theory'         # 蓝球混沌理论法
        ]
        
        for algo in algorithms:
            results['algorithm_results'][algo] = {
                'total_kills': 0,
                'successful_kills': 0,
                'success_rate': 0.0,
                'details': []
            }
        
        # 逐期测试
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break
                
            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]  # 上一期
            period2_data = self.data.iloc[i + 2]  # 上二期
            
            # 解析号码
            _, current_blue = parse_numbers(current_period)
            _, period1_blue = parse_numbers(period1_data)
            _, period2_blue = parse_numbers(period2_data)
            
            period_info = {
                'period': current_period['期号'],
                'actual_blue': current_blue,
                'prev_periods': [period1_blue, period2_blue],
                'algorithm_kills': {}
            }
            
            # 测试各个算法
            self._test_blue_individual_algorithms(period_info, algorithms, results)
            
            results['total_periods'] += 1
            results['success_details'].append(period_info)
        
        # 计算成功率
        for algo in algorithms:
            algo_result = results['algorithm_results'][algo]
            if algo_result['total_kills'] > 0:
                algo_result['success_rate'] = algo_result['successful_kills'] / algo_result['total_kills']
        
        return results
    
    def _test_blue_individual_algorithms(self, period_info: Dict, algorithms: List[str], results: Dict):
        """测试蓝球单个算法"""
        period1_blue = period_info['prev_periods'][0]
        period2_blue = period_info['prev_periods'][1]
        actual_blue = period_info['actual_blue']
        
        # 测试各个算法
        for algo in algorithms:
            if algo == 'sum':
                kills = self.blue_killer._calculate_blue_sum_kill(period1_blue, period2_blue)
            elif algo == 'diff':
                kills = self.blue_killer._calculate_blue_diff_kill(period1_blue, period2_blue)
            elif algo == 'multiple':
                kills = self.blue_killer._calculate_blue_multiple_kill(period1_blue, period2_blue)
            elif algo == 'odd_even':
                kills = self.blue_killer._calculate_blue_odd_even_kill(period1_blue, period2_blue)
            elif algo == 'blue_fib':
                kills = self._calculate_blue_fib_kill(period1_blue, period2_blue)
            elif algo == 'blue_interval':
                kills = self._calculate_blue_interval_kill(period1_blue, period2_blue)
            elif algo == 'blue_square':
                kills = self._calculate_blue_square_kill(period1_blue, period2_blue)
            elif algo == 'blue_triangle':
                kills = self._calculate_blue_triangle_kill(period1_blue, period2_blue)
            elif algo == 'blue_lucas':
                kills = self._calculate_blue_lucas_kill(period1_blue, period2_blue)
            elif algo == 'blue_perfect':
                kills = self._calculate_blue_perfect_kill(period1_blue, period2_blue)
            elif algo == 'blue_binary':
                kills = self._calculate_blue_binary_kill(period1_blue, period2_blue)
            elif algo == 'blue_modular':
                kills = self._calculate_blue_modular_kill(period1_blue, period2_blue)
            elif algo == 'blue_palindrome':
                kills = self._calculate_blue_palindrome_kill(period1_blue, period2_blue)
            elif algo == 'blue_power':
                kills = self._calculate_blue_power_kill(period1_blue, period2_blue)
            else:
                kills = []
            
            # 验证杀号效果
            valid_kills = [k for k in kills if 1 <= k <= 12 and k not in (period1_blue + period2_blue)]
            successful_kills = [k for k in valid_kills if k not in actual_blue]
            
            period_info['algorithm_kills'][algo] = {
                'kills': valid_kills,
                'successful': successful_kills,
                'success_count': len(successful_kills),
                'total_count': len(valid_kills)
            }
            
            # 更新统计
            results['algorithm_results'][algo]['total_kills'] += len(valid_kills)
            results['algorithm_results'][algo]['successful_kills'] += len(successful_kills)
            results['algorithm_results'][algo]['details'].append({
                'period': period_info['period'],
                'kills': valid_kills,
                'successful': successful_kills,
                'actual': actual_blue
            })
    
    def print_red_results(self, results: Dict):
        """打印红球测试结果"""
        print(f"\n📊 红球杀号算法测试结果 (共{results['total_periods']}期)")
        print("=" * 80)
        
        # 算法成功率排序
        algo_rates = [(algo, data['success_rate']) for algo, data in results['algorithm_results'].items()]
        algo_rates.sort(key=lambda x: x[1], reverse=True)
        
        print("🏆 算法成功率排行:")
        for i, (algo, rate) in enumerate(algo_rates, 1):
            data = results['algorithm_results'][algo]
            status = "🎯" if rate >= 0.8 else "⚠️" if rate >= 0.6 else "❌"
            print(f"  {i}. {algo:15} {rate:6.1%} ({data['successful_kills']}/{data['total_kills']}) {status}")
        
        # 详细分析
        print(f"\n📋 详细分析:")
        for algo, data in results['algorithm_results'].items():
            if data['total_kills'] > 0:
                print(f"\n{algo} 算法:")
                print(f"  总杀号数: {data['total_kills']}")
                print(f"  成功杀号: {data['successful_kills']}")
                print(f"  成功率: {data['success_rate']:.1%}")
                
                # 显示最近几期的详细情况
                print("  最近5期详情:")
                for detail in data['details'][:5]:
                    kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
                    success_str = ','.join(map(str, detail['successful'])) if detail['successful'] else '无'
                    actual_str = ','.join(map(str, detail['actual']))
                    print(f"    {detail['period']}: 杀号[{kills_str}] 成功[{success_str}] 实际[{actual_str}]")

    def _calculate_ac_value_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """AC值杀号法 - 基于号码间差值的绝对值"""
        kills = []
        all_numbers = period1 + period2

        # 计算AC值（所有号码间差值的绝对值的不重复个数）
        differences = set()
        for i in range(len(all_numbers)):
            for j in range(i + 1, len(all_numbers)):
                diff = abs(all_numbers[i] - all_numbers[j])
                differences.add(diff)

        ac_value = len(differences)

        # 基于AC值计算杀号
        kill_candidates = [
            ac_value % 35 + 1,
            (ac_value * 2) % 35 + 1,
            (ac_value + 10) % 35 + 1
        ]

        for num in kill_candidates:
            if 1 <= num <= 35:
                kills.append(num)

        return kills

    def _calculate_fibonacci_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """斐波那契杀号法 - 基于斐波那契数列"""
        kills = []

        # 斐波那契数列（35以内）
        fib_numbers = [1, 1, 2, 3, 5, 8, 13, 21, 34]

        # 计算两期号码的特征值
        sum1 = sum(period1)
        sum2 = sum(period2)

        # 基于特征值选择斐波那契数作为杀号
        fib_index = (sum1 + sum2) % len(fib_numbers)
        kills.append(fib_numbers[fib_index])

        # 添加相邻的斐波那契数
        if fib_index > 0:
            kills.append(fib_numbers[fib_index - 1])
        if fib_index < len(fib_numbers) - 1:
            kills.append(fib_numbers[fib_index + 1])

        return kills

    def _calculate_prime_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """质数杀号法 - 基于质数特性"""
        kills = []

        # 35以内的质数
        primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31]

        # 统计两期中出现的质数
        all_numbers = period1 + period2
        appeared_primes = [p for p in primes if p in all_numbers]

        # 如果质数出现过多，杀除未出现的质数
        if len(appeared_primes) >= 4:
            for prime in primes:
                if prime not in appeared_primes:
                    kills.append(prime)
                    if len(kills) >= 3:  # 最多杀3个质数
                        break

        return kills

    def _calculate_position_sum_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """位置和杀号法 - 基于位置权重和"""
        kills = []

        # 计算位置权重和
        weight_sum1 = sum(num * (i + 1) for i, num in enumerate(period1))
        weight_sum2 = sum(num * (i + 1) for i, num in enumerate(period2))

        # 基于位置权重和计算杀号
        kill_candidates = [
            weight_sum1 % 35 + 1,
            weight_sum2 % 35 + 1,
            (weight_sum1 + weight_sum2) % 35 + 1,
            abs(weight_sum1 - weight_sum2) % 35 + 1
        ]

        for num in kill_candidates:
            if 1 <= num <= 35:
                kills.append(num)

        return kills

    def _calculate_interval_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """区间杀号法 - 基于号码区间分布"""
        kills = []
        all_numbers = period1 + period2

        # 将1-35分为7个区间
        intervals = [
            (1, 5), (6, 10), (11, 15), (16, 20),
            (21, 25), (26, 30), (31, 35)
        ]

        # 统计每个区间的号码数量
        interval_counts = []
        for start, end in intervals:
            count = sum(1 for num in all_numbers if start <= num <= end)
            interval_counts.append(count)

        # 找出号码最多的区间，杀除该区间的号码
        max_count = max(interval_counts)
        if max_count >= 4:  # 如果某个区间号码过多
            max_interval_idx = interval_counts.index(max_count)
            start, end = intervals[max_interval_idx]

            # 杀除该区间中未出现的号码
            for num in range(start, end + 1):
                if num not in all_numbers:
                    kills.append(num)
                    if len(kills) >= 3:  # 最多杀3个
                        break

        return kills

    def _calculate_remainder_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """余数杀号法 - 基于除法余数"""
        kills = []
        all_numbers = period1 + period2

        # 计算各种余数分布
        for divisor in [3, 4, 5, 7]:
            remainder_counts = [0] * divisor
            for num in all_numbers:
                remainder_counts[num % divisor] += 1

            # 找出出现最多的余数
            max_count = max(remainder_counts)
            if max_count >= 3:  # 如果某个余数出现过多
                max_remainder = remainder_counts.index(max_count)

                # 杀除该余数对应的未出现号码
                for num in range(1, 36):
                    if num % divisor == max_remainder and num not in all_numbers:
                        kills.append(num)
                        if len(kills) >= 2:  # 每个除数最多杀2个
                            break

        return kills

    def _calculate_digital_root_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """数字根杀号法 - 基于数字根（各位数字和的最终单位数）"""
        kills = []

        def digital_root(n):
            while n >= 10:
                n = sum(int(digit) for digit in str(n))
            return n

        # 计算两期所有号码的数字根分布
        all_numbers = period1 + period2
        root_counts = [0] * 10  # 0-9

        for num in all_numbers:
            root = digital_root(num)
            root_counts[root] += 1

        # 找出出现最多的数字根
        max_count = max(root_counts)
        if max_count >= 3:
            max_root = root_counts.index(max_count)

            # 杀除该数字根对应的未出现号码
            for num in range(1, 36):
                if digital_root(num) == max_root and num not in all_numbers:
                    kills.append(num)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_symmetry_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """对称杀号法 - 基于号码对称性"""
        kills = []
        all_numbers = period1 + period2

        # 定义对称对（以18为中心）
        symmetry_pairs = [
            (1, 35), (2, 34), (3, 33), (4, 32), (5, 31),
            (6, 30), (7, 29), (8, 28), (9, 27), (10, 26),
            (11, 25), (12, 24), (13, 23), (14, 22), (15, 21),
            (16, 20), (17, 19)
        ]

        # 检查对称对的出现情况
        for num1, num2 in symmetry_pairs:
            if num1 in all_numbers and num2 not in all_numbers:
                # 如果对称的一边出现了，杀除另一边
                kills.append(num2)
            elif num2 in all_numbers and num1 not in all_numbers:
                kills.append(num1)

            if len(kills) >= 4:  # 最多杀4个对称号码
                break

        return kills

    def _calculate_triangle_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """三角数杀号法 - 基于三角数序列 T(n) = n(n+1)/2"""
        kills = []

        # 35以内的三角数
        triangle_numbers = []
        n = 1
        while True:
            triangle = n * (n + 1) // 2
            if triangle > 35:
                break
            triangle_numbers.append(triangle)
            n += 1

        all_numbers = period1 + period2
        appeared_triangles = [t for t in triangle_numbers if t in all_numbers]

        # 如果三角数出现过多，杀除未出现的三角数
        if len(appeared_triangles) >= 2:
            for triangle in triangle_numbers:
                if triangle not in appeared_triangles:
                    kills.append(triangle)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_square_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """平方数杀号法 - 基于完全平方数"""
        kills = []

        # 35以内的完全平方数
        square_numbers = [1, 4, 9, 16, 25]

        all_numbers = period1 + period2
        appeared_squares = [s for s in square_numbers if s in all_numbers]

        # 如果平方数出现过多，杀除未出现的平方数
        if len(appeared_squares) >= 2:
            for square in square_numbers:
                if square not in appeared_squares:
                    kills.append(square)

        return kills

    def _calculate_lucas_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """卢卡斯数列杀号法 - L(n) = L(n-1) + L(n-2), L(0)=2, L(1)=1"""
        kills = []

        # 35以内的卢卡斯数
        lucas_numbers = [2, 1, 3, 4, 7, 11, 18, 29]

        # 计算特征值
        sum1 = sum(period1)
        sum2 = sum(period2)

        # 基于特征值选择卢卡斯数
        lucas_index = (sum1 + sum2) % len(lucas_numbers)
        kills.append(lucas_numbers[lucas_index])

        # 添加相邻的卢卡斯数
        if lucas_index > 0:
            kills.append(lucas_numbers[lucas_index - 1])

        return kills

    def _calculate_catalan_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """卡塔兰数杀号法 - C(n) = (2n)! / ((n+1)! * n!)"""
        kills = []

        # 35以内的卡塔兰数
        catalan_numbers = [1, 1, 2, 5, 14]

        all_numbers = period1 + period2

        # 计算数字特征
        digit_sum = sum(int(d) for num in all_numbers for d in str(num))

        # 基于数字特征选择卡塔兰数
        catalan_index = digit_sum % len(catalan_numbers)
        if catalan_numbers[catalan_index] not in all_numbers:
            kills.append(catalan_numbers[catalan_index])

        return kills

    def _calculate_harmonic_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """调和数杀号法 - 基于调和级数的近似值"""
        kills = []
        all_numbers = period1 + period2

        # 计算调和数的近似值
        harmonic_sum = sum(1.0 / num for num in all_numbers if num > 0)

        # 基于调和和计算杀号
        harmonic_kill = int(harmonic_sum * 10) % 35 + 1
        if harmonic_kill not in all_numbers:
            kills.append(harmonic_kill)

        # 添加调和数的倍数
        harmonic_multiple = (int(harmonic_sum * 20)) % 35 + 1
        if harmonic_multiple not in all_numbers and harmonic_multiple != harmonic_kill:
            kills.append(harmonic_multiple)

        return kills

    def _calculate_pentagonal_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """五边形数杀号法 - P(n) = n(3n-1)/2"""
        kills = []

        # 35以内的五边形数
        pentagonal_numbers = []
        n = 1
        while True:
            pent = n * (3 * n - 1) // 2
            if pent > 35:
                break
            pentagonal_numbers.append(pent)
            n += 1

        all_numbers = period1 + period2
        appeared_pent = [p for p in pentagonal_numbers if p in all_numbers]

        # 如果五边形数出现较多，杀除未出现的
        if len(appeared_pent) >= 1:
            for pent in pentagonal_numbers:
                if pent not in appeared_pent:
                    kills.append(pent)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_binary_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """二进制杀号法 - 基于二进制表示"""
        kills = []
        all_numbers = period1 + period2

        # 统计二进制位数分布
        bit_counts = {}
        for num in all_numbers:
            bit_count = bin(num).count('1')  # 计算二进制中1的个数
            bit_counts[bit_count] = bit_counts.get(bit_count, 0) + 1

        # 找出出现最多的位数
        if bit_counts:
            max_bit_count = max(bit_counts, key=bit_counts.get)

            # 杀除具有相同位数的未出现号码
            for num in range(1, 36):
                if bin(num).count('1') == max_bit_count and num not in all_numbers:
                    kills.append(num)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_factorial_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """阶乘杀号法 - 基于阶乘数"""
        kills = []

        # 35以内的阶乘数
        factorial_numbers = [1, 2, 6, 24]  # 1!, 2!, 3!, 4!

        all_numbers = period1 + period2
        appeared_factorials = [f for f in factorial_numbers if f in all_numbers]

        # 如果阶乘数出现，杀除其他阶乘数
        if appeared_factorials:
            for factorial in factorial_numbers:
                if factorial not in appeared_factorials:
                    kills.append(factorial)

        return kills

    def _calculate_power_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """幂次杀号法 - 基于2的幂次"""
        kills = []

        # 35以内的2的幂次
        power_numbers = [1, 2, 4, 8, 16, 32]  # 2^0, 2^1, 2^2, 2^3, 2^4, 2^5

        all_numbers = period1 + period2
        appeared_powers = [p for p in power_numbers if p in all_numbers]

        # 如果幂次数出现过多，杀除未出现的
        if len(appeared_powers) >= 2:
            for power in power_numbers:
                if power not in appeared_powers:
                    kills.append(power)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_modular_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """模运算杀号法 - 基于模运算规律"""
        kills = []
        all_numbers = period1 + period2

        # 对多个模数进行分析
        for mod in [6, 7, 8, 9]:
            remainder_counts = [0] * mod
            for num in all_numbers:
                remainder_counts[num % mod] += 1

            # 找出出现最少的余数
            min_count = min(remainder_counts)
            min_remainders = [i for i, count in enumerate(remainder_counts) if count == min_count]

            # 杀除对应余数的号码
            for remainder in min_remainders:
                for num in range(remainder, 36, mod):
                    if num >= 1 and num not in all_numbers:
                        kills.append(num)
                        if len(kills) >= 2:
                            break
                if len(kills) >= 2:
                    break

            if len(kills) >= 2:
                break

        return kills

    def _calculate_gcd_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """最大公约数杀号法"""
        kills = []
        all_numbers = period1 + period2

        import math

        # 计算所有号码的最大公约数
        if len(all_numbers) >= 2:
            gcd_value = all_numbers[0]
            for num in all_numbers[1:]:
                gcd_value = math.gcd(gcd_value, num)

            # 基于GCD计算杀号
            if gcd_value > 1:
                # 杀除GCD的倍数
                for multiple in range(gcd_value, 36, gcd_value):
                    if multiple not in all_numbers:
                        kills.append(multiple)
                        if len(kills) >= 3:
                            break

        return kills

    def _calculate_lcm_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """最小公倍数杀号法"""
        kills = []
        all_numbers = period1 + period2

        import math

        # 计算前几个数的最小公倍数
        if len(all_numbers) >= 2:
            lcm_value = all_numbers[0]
            for num in all_numbers[1:3]:  # 只取前3个数避免LCM过大
                lcm_value = lcm_value * num // math.gcd(lcm_value, num)
                if lcm_value > 35:
                    break

            # 基于LCM计算杀号
            if lcm_value <= 35 and lcm_value not in all_numbers:
                kills.append(lcm_value)

        return kills

    def _calculate_palindrome_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """回文数杀号法"""
        kills = []

        # 35以内的回文数
        palindrome_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 22, 33]

        all_numbers = period1 + period2
        appeared_palindromes = [p for p in palindrome_numbers if p in all_numbers]

        # 如果回文数出现过多，杀除未出现的
        if len(appeared_palindromes) >= 3:
            for palindrome in palindrome_numbers:
                if palindrome not in appeared_palindromes:
                    kills.append(palindrome)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_perfect_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """完全数杀号法 - 等于其真因数之和的数"""
        kills = []

        # 35以内的完全数
        perfect_numbers = [6, 28]  # 6 = 1+2+3, 28 = 1+2+4+7+14

        all_numbers = period1 + period2

        # 如果完全数出现，杀除其他完全数
        for perfect in perfect_numbers:
            if perfect in all_numbers:
                for other_perfect in perfect_numbers:
                    if other_perfect != perfect and other_perfect not in all_numbers:
                        kills.append(other_perfect)

        return kills

    def _calculate_abundant_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """过剩数杀号法 - 真因数之和大于自身的数"""
        kills = []

        def sum_of_proper_divisors(n):
            return sum(i for i in range(1, n) if n % i == 0)

        # 35以内的过剩数
        abundant_numbers = []
        for num in range(1, 36):
            if sum_of_proper_divisors(num) > num:
                abundant_numbers.append(num)

        all_numbers = period1 + period2
        appeared_abundant = [a for a in abundant_numbers if a in all_numbers]

        # 如果过剩数出现过多，杀除未出现的
        if len(appeared_abundant) >= 2:
            for abundant in abundant_numbers:
                if abundant not in appeared_abundant:
                    kills.append(abundant)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_deficient_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """亏数杀号法 - 真因数之和小于自身的数"""
        kills = []

        def sum_of_proper_divisors(n):
            return sum(i for i in range(1, n) if n % i == 0)

        all_numbers = period1 + period2

        # 统计亏数的出现情况
        deficient_count = 0
        for num in all_numbers:
            if sum_of_proper_divisors(num) < num:
                deficient_count += 1

        # 如果亏数出现过多，杀除一些亏数
        if deficient_count >= 4:
            for num in range(1, 36):
                if (sum_of_proper_divisors(num) < num and
                    num not in all_numbers):
                    kills.append(num)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_blue_prime_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球质数杀号法"""
        kills = []

        # 12以内的质数
        blue_primes = [2, 3, 5, 7, 11]
        all_blue = period1_blue + period2_blue

        # 统计出现的质数
        appeared_primes = [p for p in blue_primes if p in all_blue]

        # 如果质数出现较多，杀除未出现的质数
        if len(appeared_primes) >= 2:
            for prime in blue_primes:
                if prime not in appeared_primes:
                    kills.append(prime)

        return kills

    def _calculate_blue_fib_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球斐波那契杀号法"""
        kills = []

        # 12以内的斐波那契数
        blue_fib = [1, 2, 3, 5, 8]
        all_blue = period1_blue + period2_blue

        # 计算特征值
        blue_sum = sum(all_blue)

        # 基于特征值选择斐波那契数杀号
        fib_index = blue_sum % len(blue_fib)
        kills.append(blue_fib[fib_index])

        return kills

    def _calculate_blue_digital_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球数字根杀号法"""
        kills = []

        def digital_root(n):
            while n >= 10:
                n = sum(int(digit) for digit in str(n))
            return n

        all_blue = period1_blue + period2_blue

        # 统计数字根分布
        root_counts = [0] * 10
        for num in all_blue:
            root = digital_root(num)
            root_counts[root] += 1

        # 找出出现最多的数字根
        max_count = max(root_counts)
        if max_count >= 2:
            max_root = root_counts.index(max_count)

            # 杀除该数字根对应的未出现蓝球
            for num in range(1, 13):
                if digital_root(num) == max_root and num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_interval_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球区间杀号法"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 将1-12分为3个区间
        intervals = [(1, 4), (5, 8), (9, 12)]

        # 统计每个区间的蓝球数量
        interval_counts = []
        for start, end in intervals:
            count = sum(1 for num in all_blue if start <= num <= end)
            interval_counts.append(count)

        # 找出蓝球最多的区间
        max_count = max(interval_counts)
        if max_count >= 2:
            max_interval_idx = interval_counts.index(max_count)
            start, end = intervals[max_interval_idx]

            # 杀除该区间中未出现的蓝球
            for num in range(start, end + 1):
                if num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_square_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球平方数杀号法"""
        kills = []

        # 12以内的平方数
        blue_squares = [1, 4, 9]  # 1^2, 2^2, 3^2
        all_blue = period1_blue + period2_blue

        appeared_squares = [s for s in blue_squares if s in all_blue]

        # 如果平方数出现，杀除其他平方数
        if appeared_squares:
            for square in blue_squares:
                if square not in appeared_squares:
                    kills.append(square)

        return kills

    def _calculate_blue_triangle_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球三角数杀号法"""
        kills = []

        # 12以内的三角数
        blue_triangles = [1, 3, 6, 10]  # T(1), T(2), T(3), T(4)
        all_blue = period1_blue + period2_blue

        appeared_triangles = [t for t in blue_triangles if t in all_blue]

        # 如果三角数出现过多，杀除未出现的
        if len(appeared_triangles) >= 2:
            for triangle in blue_triangles:
                if triangle not in appeared_triangles:
                    kills.append(triangle)

        return kills

    def _calculate_blue_lucas_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球卢卡斯数杀号法"""
        kills = []

        # 12以内的卢卡斯数
        blue_lucas = [2, 1, 3, 4, 7, 11]
        all_blue = period1_blue + period2_blue

        # 计算特征值
        blue_sum = sum(all_blue)

        # 基于特征值选择卢卡斯数
        lucas_index = blue_sum % len(blue_lucas)
        if blue_lucas[lucas_index] not in all_blue:
            kills.append(blue_lucas[lucas_index])

        return kills

    def _calculate_blue_perfect_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球完全数杀号法"""
        kills = []

        # 12以内的完全数
        blue_perfect = [6]  # 6 = 1+2+3
        all_blue = period1_blue + period2_blue

        # 如果完全数出现，根据规律杀号
        if 6 in all_blue:
            # 杀除与6相关的数字
            related_numbers = [3, 12]  # 6的一半和两倍
            for num in related_numbers:
                if num not in all_blue:
                    kills.append(num)

        return kills

    def _calculate_blue_binary_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球二进制杀号法"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 统计二进制位数分布
        bit_counts = {}
        for num in all_blue:
            bit_count = bin(num).count('1')
            bit_counts[bit_count] = bit_counts.get(bit_count, 0) + 1

        # 找出出现最多的位数
        if bit_counts:
            max_bit_count = max(bit_counts, key=bit_counts.get)

            # 杀除具有相同位数的未出现蓝球
            for num in range(1, 13):
                if bin(num).count('1') == max_bit_count and num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_modular_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球模运算杀号法"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 对模3和模4进行分析
        for mod in [3, 4]:
            remainder_counts = [0] * mod
            for num in all_blue:
                remainder_counts[num % mod] += 1

            # 找出出现最多的余数
            max_count = max(remainder_counts)
            max_remainder = remainder_counts.index(max_count)

            # 杀除该余数对应的未出现蓝球
            for num in range(1, 13):
                if num % mod == max_remainder and num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 1:
                        break

            if kills:
                break

        return kills

    def _calculate_blue_palindrome_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球回文数杀号法"""
        kills = []

        # 12以内的回文数
        blue_palindromes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 11]
        all_blue = period1_blue + period2_blue

        appeared_palindromes = [p for p in blue_palindromes if p in all_blue]

        # 如果回文数出现过多，杀除未出现的
        if len(appeared_palindromes) >= 2:
            for palindrome in blue_palindromes:
                if palindrome not in appeared_palindromes:
                    kills.append(palindrome)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_power_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球幂次杀号法"""
        kills = []

        # 12以内的2的幂次
        blue_powers = [1, 2, 4, 8]  # 2^0, 2^1, 2^2, 2^3
        all_blue = period1_blue + period2_blue

        appeared_powers = [p for p in blue_powers if p in all_blue]

        # 如果幂次数出现，杀除其他幂次数
        if appeared_powers:
            for power in blue_powers:
                if power not in appeared_powers:
                    kills.append(power)
                    if len(kills) >= 2:
                        break

        return kills

    def print_blue_results(self, results: Dict):
        """打印蓝球测试结果"""
        print(f"\n📊 蓝球杀号算法测试结果 (共{results['total_periods']}期)")
        print("=" * 80)

        # 算法成功率排序
        algo_rates = [(algo, data['success_rate']) for algo, data in results['algorithm_results'].items()]
        algo_rates.sort(key=lambda x: x[1], reverse=True)

        print("🏆 算法成功率排行:")
        for i, (algo, rate) in enumerate(algo_rates, 1):
            data = results['algorithm_results'][algo]
            status = "🎯" if rate >= 0.8 else "⚠️" if rate >= 0.6 else "❌"
            print(f"  {i}. {algo:15} {rate:6.1%} ({data['successful_kills']}/{data['total_kills']}) {status}")

        # 详细分析
        print(f"\n📋 详细分析:")
        for algo, data in results['algorithm_results'].items():
            if data['total_kills'] > 0:
                print(f"\n{algo} 算法:")
                print(f"  总杀号数: {data['total_kills']}")
                print(f"  成功杀号: {data['successful_kills']}")
                print(f"  成功率: {data['success_rate']:.1%}")

                # 显示最近几期的详细情况
                print("  最近5期详情:")
                for detail in data['details'][:5]:
                    kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
                    success_str = ','.join(map(str, detail['successful'])) if detail['successful'] else '无'
                    actual_str = ','.join(map(str, detail['actual']))
                    print(f"    {detail['period']}: 杀号[{kills_str}] 成功[{success_str}] 实际[{actual_str}]")

    def test_combined_strategies(self, test_periods: int = 20):
        """测试组合杀号策略"""
        print(f"\n🔍 测试组合杀号策略 (最近{test_periods}期)")
        print("=" * 60)

        strategies = {
            'conservative': {'min_votes': 3, 'max_kills': 2},  # 保守策略：需要3个算法支持，最多杀2个
            'moderate': {'min_votes': 2, 'max_kills': 4},      # 中等策略：需要2个算法支持，最多杀4个
            'aggressive': {'min_votes': 1, 'max_kills': 6}     # 激进策略：需要1个算法支持，最多杀6个
        }

        results = {}

        for strategy_name, params in strategies.items():
            print(f"\n测试 {strategy_name} 策略 (最少{params['min_votes']}票，最多{params['max_kills']}个杀号):")

            total_periods = 0
            total_kills = 0
            successful_kills = 0
            strategy_details = []

            for i in range(test_periods):
                if i + 2 >= len(self.data):
                    break

                # 获取数据
                current_period = self.data.iloc[i]
                period1_data = self.data.iloc[i + 1]
                period2_data = self.data.iloc[i + 2]

                current_red, _ = parse_numbers(current_period)
                period1_red, _ = parse_numbers(period1_data)
                period2_red, _ = parse_numbers(period2_data)

                # 收集所有算法的投票
                algorithm_votes = {}

                # 各算法投票 (使用有效的算法)
                algorithms = [
                    ('min_sum', self.red_killer._calculate_min_two_sum_kill(period1_red, period2_red)),
                    ('max_diff', self.red_killer._calculate_max_two_diff_kill(period1_red, period2_red)),
                    ('sum_tail', self.red_killer._calculate_sum_tail_kill(period1_red, period2_red)),
                    ('span', self.red_killer._calculate_span_kill(period1_red, period2_red)),
                    ('odd_even', self.red_killer._calculate_odd_even_kill(period1_red, period2_red)),
                    ('consecutive', self.red_killer._calculate_consecutive_kill(period1_red, period2_red)),
                    ('ac_value', self._calculate_ac_value_kill(period1_red, period2_red)),
                    ('fibonacci', self._calculate_fibonacci_kill(period1_red, period2_red)),
                    ('position_sum', self._calculate_position_sum_kill(period1_red, period2_red)),
                    ('interval_kill', self._calculate_interval_kill(period1_red, period2_red)),
                    ('remainder_kill', self._calculate_remainder_kill(period1_red, period2_red)),
                    ('digital_root', self._calculate_digital_root_kill(period1_red, period2_red)),
                    ('symmetry_kill', self._calculate_symmetry_kill(period1_red, period2_red)),
                    ('triangle_kill', self._calculate_triangle_kill(period1_red, period2_red)),
                    ('lucas_kill', self._calculate_lucas_kill(period1_red, period2_red)),
                    ('catalan_kill', self._calculate_catalan_kill(period1_red, period2_red)),
                    ('harmonic_kill', self._calculate_harmonic_kill(period1_red, period2_red)),
                    ('pentagonal_kill', self._calculate_pentagonal_kill(period1_red, period2_red)),
                    ('binary_kill', self._calculate_binary_kill(period1_red, period2_red)),
                    ('factorial_kill', self._calculate_factorial_kill(period1_red, period2_red)),
                    ('modular_kill', self._calculate_modular_kill(period1_red, period2_red)),
                    ('palindrome_kill', self._calculate_palindrome_kill(period1_red, period2_red)),
                    ('abundant_kill', self._calculate_abundant_kill(period1_red, period2_red)),
                    ('deficient_kill', self._calculate_deficient_kill(period1_red, period2_red))
                ]

                for algo_name, kills in algorithms:
                    for kill in kills:
                        if 1 <= kill <= 35 and kill not in (period1_red + period2_red):
                            if kill not in algorithm_votes:
                                algorithm_votes[kill] = []
                            algorithm_votes[kill].append(algo_name)

                # 根据策略筛选杀号
                strategy_kills = []
                for num, votes in algorithm_votes.items():
                    if len(votes) >= params['min_votes']:
                        strategy_kills.append((num, len(votes)))

                # 按票数排序，取前N个
                strategy_kills.sort(key=lambda x: x[1], reverse=True)
                final_kills = [num for num, votes in strategy_kills[:params['max_kills']]]

                # 验证效果
                successful = [k for k in final_kills if k not in current_red]

                period_detail = {
                    'period': current_period['期号'],
                    'kills': final_kills,
                    'successful': successful,
                    'actual': current_red,
                    'success_rate': len(successful) / len(final_kills) if final_kills else 1.0
                }

                strategy_details.append(period_detail)
                total_periods += 1
                total_kills += len(final_kills)
                successful_kills += len(successful)

                # 打印详情
                kills_str = ','.join(map(str, final_kills)) if final_kills else '无'
                success_str = ','.join(map(str, successful)) if successful else '无'
                actual_str = ','.join(map(str, current_red))
                success_rate = len(successful) / len(final_kills) if final_kills else 1.0
                status = "✅" if success_rate == 1.0 else "⚠️" if success_rate >= 0.8 else "❌"
                print(f"  {current_period['期号']}: 杀号[{kills_str}] 成功[{success_str}] 成功率{success_rate:.1%} {status}")

            # 策略总结
            overall_success_rate = successful_kills / total_kills if total_kills > 0 else 1.0
            results[strategy_name] = {
                'total_periods': total_periods,
                'total_kills': total_kills,
                'successful_kills': successful_kills,
                'success_rate': overall_success_rate,
                'details': strategy_details
            }

            print(f"  📊 {strategy_name} 策略总结:")
            print(f"    总期数: {total_periods}")
            print(f"    总杀号: {total_kills}")
            print(f"    成功杀号: {successful_kills}")
            print(f"    成功率: {overall_success_rate:.1%}")

        return results

    def find_best_algorithms(self, red_results: Dict, blue_results: Dict):
        """找出最佳算法组合"""
        print(f"\n🎯 最佳算法组合分析")
        print("=" * 60)

        # 红球最佳算法
        print("🔴 红球最佳算法:")
        red_algos = [(algo, data['success_rate']) for algo, data in red_results['algorithm_results'].items()
                     if data['total_kills'] > 0]
        red_algos.sort(key=lambda x: x[1], reverse=True)

        for i, (algo, rate) in enumerate(red_algos[:3], 1):
            data = red_results['algorithm_results'][algo]
            print(f"  {i}. {algo}: {rate:.1%} ({data['successful_kills']}/{data['total_kills']})")

        # 蓝球最佳算法
        print("\n🔵 蓝球最佳算法:")
        blue_algos = [(algo, data['success_rate']) for algo, data in blue_results['algorithm_results'].items()
                      if data['total_kills'] > 0]
        blue_algos.sort(key=lambda x: x[1], reverse=True)

        for i, (algo, rate) in enumerate(blue_algos[:3], 1):
            data = blue_results['algorithm_results'][algo]
            print(f"  {i}. {algo}: {rate:.1%} ({data['successful_kills']}/{data['total_kills']})")

        # 推荐组合
        print(f"\n💡 推荐杀号策略:")
        if red_algos:
            best_red = red_algos[0]
            print(f"  红球主力算法: {best_red[0]} (成功率{best_red[1]:.1%})")

        if blue_algos:
            best_blue = blue_algos[0]
            print(f"  蓝球主力算法: {best_blue[0]} (成功率{best_blue[1]:.1%})")


def main():
    """主函数"""
    print("🎯 杀号算法专门测试系统")
    print("=" * 60)

    # 初始化测试器
    tester = KillAlgorithmTester()

    # 加载数据
    if not tester.load_data():
        return

    # 测试红球杀号算法
    red_results = tester.test_red_kill_algorithms(test_periods=30)
    tester.print_red_results(red_results)

    # 测试蓝球杀号算法
    blue_results = tester.test_blue_kill_algorithms(test_periods=30)
    tester.print_blue_results(blue_results)

    # 测试组合策略
    combined_results = tester.test_combined_strategies(test_periods=20)

    # 找出最佳算法
    tester.find_best_algorithms(red_results, blue_results)

    print(f"\n🎉 测试完成！")


if __name__ == "__main__":
    main()
