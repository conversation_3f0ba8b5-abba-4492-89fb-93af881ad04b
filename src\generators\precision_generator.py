"""
精准命中生成器
专门针对2+1命中率优化，重点提高命中精度而非覆盖面
"""

import numpy as np
from typing import List, Tuple, Dict, Set
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, state_to_ratio, load_data
)


class PrecisionGenerator:
    """精准命中生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
        
        # 分析历史数据，找出最容易命中的号码模式
        self.data = load_data()
        self._analyze_winning_patterns()
        
        # 精准策略：重点关注最可能出现的号码
        self.precision_weights = {
            'super_hot': 0.6,        # 超级热号（最近20期高频）
            'position_hot': 0.3,     # 位置热号（按位置分析）
            'pattern_match': 0.1     # 模式匹配
        }
    
    def _analyze_winning_patterns(self) -> None:
        """分析中奖模式，找出最容易命中的号码"""
        # 分析最近30期的超高频号码
        recent_data = self.data.head(30)
        
        red_freq = Counter()
        blue_freq = Counter()
        
        # 位置频率分析
        red_position_freq = [Counter() for _ in range(5)]
        blue_position_freq = [Counter() for _ in range(2)]
        
        for _, row in recent_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq.update(red_balls)
            blue_freq.update(blue_balls)
            
            # 按位置统计
            sorted_red = sorted(red_balls)
            for pos, num in enumerate(sorted_red):
                red_position_freq[pos][num] += 1
            
            sorted_blue = sorted(blue_balls)
            for pos, num in enumerate(sorted_blue):
                blue_position_freq[pos][num] += 1
        
        # 超级热号（出现频率 > 平均值 * 1.5）
        avg_red_freq = sum(red_freq.values()) / len(red_freq)
        avg_blue_freq = sum(blue_freq.values()) / len(blue_freq)
        
        self.super_hot_red = [num for num, freq in red_freq.items() if freq > avg_red_freq * 1.2]
        self.super_hot_blue = [num for num, freq in blue_freq.items() if freq > avg_blue_freq * 1.2]
        
        # 位置热号（每个位置最热的3个号码）
        self.position_hot_red = []
        for pos_freq in red_position_freq:
            hot_nums = [num for num, _ in pos_freq.most_common(3)]
            self.position_hot_red.extend(hot_nums)
        
        self.position_hot_blue = []
        for pos_freq in blue_position_freq:
            hot_nums = [num for num, _ in pos_freq.most_common(2)]
            self.position_hot_blue.extend(hot_nums)
        
        # 去重
        self.super_hot_red = list(set(self.super_hot_red))
        self.super_hot_blue = list(set(self.super_hot_blue))
        self.position_hot_red = list(set(self.position_hot_red))
        self.position_hot_blue = list(set(self.position_hot_blue))
        
        print(f"超级热红球: {sorted(self.super_hot_red)}")
        print(f"超级热蓝球: {sorted(self.super_hot_blue)}")
        print(f"位置热红球: {sorted(self.position_hot_red)}")
        print(f"位置热蓝球: {sorted(self.position_hot_blue)}")
        
        # 分析最近的中奖模式
        self._analyze_recent_winners()
    
    def _analyze_recent_winners(self) -> None:
        """分析最近的中奖模式"""
        # 分析最近10期的号码特征
        recent_winners = []
        for i in range(10):
            try:
                row = self.data.iloc[i]
                red_balls, blue_balls = parse_numbers(row)
                
                # 分析特征
                red_sum = sum(red_balls)
                red_span = max(red_balls) - min(red_balls)
                blue_sum = sum(blue_balls)
                
                recent_winners.append({
                    'red': red_balls,
                    'blue': blue_balls,
                    'red_sum': red_sum,
                    'red_span': red_span,
                    'blue_sum': blue_sum
                })
            except:
                continue
        
        # 找出最常见的特征范围
        red_sums = [w['red_sum'] for w in recent_winners]
        red_spans = [w['red_span'] for w in recent_winners]
        blue_sums = [w['blue_sum'] for w in recent_winners]
        
        self.optimal_red_sum_range = (int(np.mean(red_sums) - 15), int(np.mean(red_sums) + 15))
        self.optimal_red_span_range = (int(np.mean(red_spans) - 5), int(np.mean(red_spans) + 5))
        self.optimal_blue_sum_range = (int(np.mean(blue_sums) - 3), int(np.mean(blue_sums) + 3))
        
        print(f"最优红球和值范围: {self.optimal_red_sum_range}")
        print(f"最优红球跨度范围: {self.optimal_red_span_range}")
        print(f"最优蓝球和值范围: {self.optimal_blue_sum_range}")
    
    def generate_precision_numbers(self, 
                                 red_odd_even_state: str,
                                 red_size_state: str,
                                 blue_size_state: str,
                                 kill_numbers: Dict[str, List[List[int]]] = None,
                                 seed: int = 0) -> Tuple[List[int], List[int]]:
        """
        生成精准命中号码
        
        Args:
            red_odd_even_state: 红球奇偶比状态
            red_size_state: 红球大小比状态
            blue_size_state: 蓝球大小比状态
            kill_numbers: 杀号列表
            seed: 随机种子
            
        Returns:
            Tuple[List[int], List[int]]: (红球, 蓝球)
        """
        np.random.seed(seed)
        
        # 精准红球生成
        red_balls = self._generate_precision_red(
            red_odd_even_state, red_size_state, kill_numbers, seed
        )
        
        # 精准蓝球生成
        blue_balls = self._generate_precision_blue(
            blue_size_state, kill_numbers, seed
        )
        
        print(f"  精准策略: 红球{len(set(red_balls) & set(self.super_hot_red))}/5个超热号, "
              f"蓝球{len(set(blue_balls) & set(self.super_hot_blue))}/2个超热号")
        
        return sorted(red_balls), sorted(blue_balls)
    
    def _generate_precision_red(self, odd_even_state: str, size_state: str, 
                              kill_numbers: Dict, seed: int) -> List[int]:
        """生成精准红球"""
        np.random.seed(seed)
        
        # 创建精准候选池
        precision_candidates = []
        
        # 60% 超级热号
        super_hot_filtered = self._apply_kill_filter(self.super_hot_red, kill_numbers, 'red')
        precision_candidates.extend(super_hot_filtered)
        
        # 30% 位置热号
        position_hot_filtered = self._apply_kill_filter(self.position_hot_red, kill_numbers, 'red')
        precision_candidates.extend([n for n in position_hot_filtered if n not in precision_candidates])
        
        # 去重并确保有足够候选
        precision_candidates = list(set(precision_candidates))
        
        if len(precision_candidates) < 8:
            # 补充其他高频号码
            all_filtered = self._apply_kill_filter(self.red_range, kill_numbers, 'red')
            remaining = [n for n in all_filtered if n not in precision_candidates]
            precision_candidates.extend(remaining[:8 - len(precision_candidates)])
        
        # 多次尝试生成最优组合
        best_combination = None
        best_score = -1
        
        for attempt in range(30):
            try:
                combination = self._select_optimal_red_combination(
                    precision_candidates, odd_even_state, size_state, seed + attempt
                )
                
                if combination and len(set(combination)) == 5:
                    score = self._evaluate_red_precision(combination)
                    if score > best_score:
                        best_score = score
                        best_combination = combination
            except:
                continue
        
        if not best_combination:
            # 备选方案：直接从精准候选中随机选择
            if len(precision_candidates) >= 5:
                best_combination = list(np.random.choice(precision_candidates, 5, replace=False))
            else:
                best_combination = precision_candidates + [1, 2, 3, 4, 5][:5-len(precision_candidates)]
        
        return best_combination[:5]
    
    def _select_optimal_red_combination(self, candidates: List[int], 
                                      odd_even_state: str, size_state: str, 
                                      seed: int) -> List[int]:
        """选择最优红球组合"""
        np.random.seed(seed)
        
        # 解析状态要求
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)
        
        # 按奇偶和大小分类
        odd_candidates = [n for n in candidates if n % 2 == 1]
        even_candidates = [n for n in candidates if n % 2 == 0]
        small_candidates = [n for n in candidates if 1 <= n <= 18]
        big_candidates = [n for n in candidates if 19 <= n <= 35]
        
        selected = []
        
        # 优先从超级热号中选择
        super_hot_in_candidates = [n for n in candidates if n in self.super_hot_red]
        
        # 先选择超级热号中满足条件的
        for num in super_hot_in_candidates:
            if len(selected) >= 5:
                break
            
            # 检查是否满足奇偶和大小要求
            current_odd = sum(1 for n in selected if n % 2 == 1)
            current_even = sum(1 for n in selected if n % 2 == 0)
            current_small = sum(1 for n in selected if 1 <= n <= 18)
            current_big = sum(1 for n in selected if 19 <= n <= 35)
            
            can_add = True
            
            # 检查奇偶约束
            if num % 2 == 1 and current_odd >= odd_count:
                can_add = False
            elif num % 2 == 0 and current_even >= even_count:
                can_add = False
            
            # 检查大小约束
            if 1 <= num <= 18 and current_small >= small_count:
                can_add = False
            elif 19 <= num <= 35 and current_big >= big_count:
                can_add = False
            
            if can_add:
                selected.append(num)
        
        # 如果还需要更多号码，从其他候选中选择
        remaining_count = 5 - len(selected)
        if remaining_count > 0:
            remaining_candidates = [n for n in candidates if n not in selected]
            
            # 尝试满足剩余的奇偶和大小要求
            current_odd = sum(1 for n in selected if n % 2 == 1)
            current_even = sum(1 for n in selected if n % 2 == 0)
            
            need_odd = max(0, odd_count - current_odd)
            need_even = max(0, even_count - current_even)
            
            # 选择奇数
            if need_odd > 0:
                available_odd = [n for n in remaining_candidates if n % 2 == 1]
                if available_odd:
                    selected.extend(list(np.random.choice(
                        available_odd, 
                        min(need_odd, len(available_odd), remaining_count), 
                        replace=False
                    )))
            
            # 选择偶数
            remaining_count = 5 - len(selected)
            if need_even > 0 and remaining_count > 0:
                available_even = [n for n in remaining_candidates if n % 2 == 0 and n not in selected]
                if available_even:
                    selected.extend(list(np.random.choice(
                        available_even,
                        min(need_even, len(available_even), remaining_count),
                        replace=False
                    )))
            
            # 如果还不够，随机补充
            remaining_count = 5 - len(selected)
            if remaining_count > 0:
                final_candidates = [n for n in remaining_candidates if n not in selected]
                if final_candidates:
                    selected.extend(list(np.random.choice(
                        final_candidates,
                        min(remaining_count, len(final_candidates)),
                        replace=False
                    )))
        
        return selected[:5]
    
    def _generate_precision_blue(self, size_state: str, kill_numbers: Dict, seed: int) -> List[int]:
        """生成精准蓝球"""
        np.random.seed(seed)
        
        # 蓝球精准策略：优先超级热号
        precision_candidates = []
        
        # 80% 超级热号
        super_hot_filtered = self._apply_kill_filter(self.super_hot_blue, kill_numbers, 'blue')
        precision_candidates.extend(super_hot_filtered)
        
        # 20% 位置热号
        position_hot_filtered = self._apply_kill_filter(self.position_hot_blue, kill_numbers, 'blue')
        precision_candidates.extend([n for n in position_hot_filtered if n not in precision_candidates])
        
        # 去重
        precision_candidates = list(set(precision_candidates))
        
        if len(precision_candidates) < 4:
            # 补充其他候选
            all_filtered = self._apply_kill_filter(self.blue_range, kill_numbers, 'blue')
            remaining = [n for n in all_filtered if n not in precision_candidates]
            precision_candidates.extend(remaining[:4 - len(precision_candidates)])
        
        # 按大小比要求选择
        small_count, big_count = state_to_ratio(size_state)
        
        small_candidates = [n for n in precision_candidates if 1 <= n <= 6]
        big_candidates = [n for n in precision_candidates if 7 <= n <= 12]
        
        selected = []
        
        # 选择小号
        if small_count > 0 and small_candidates:
            # 优先选择超级热号中的小号
            super_hot_small = [n for n in small_candidates if n in self.super_hot_blue]
            if super_hot_small:
                selected.extend(list(np.random.choice(
                    super_hot_small,
                    min(small_count, len(super_hot_small)),
                    replace=False
                )))
            else:
                selected.extend(list(np.random.choice(
                    small_candidates,
                    min(small_count, len(small_candidates)),
                    replace=False
                )))
        
        # 选择大号
        remaining_count = 2 - len(selected)
        if big_count > 0 and big_candidates and remaining_count > 0:
            available_big = [n for n in big_candidates if n not in selected]
            if available_big:
                # 优先选择超级热号中的大号
                super_hot_big = [n for n in available_big if n in self.super_hot_blue]
                if super_hot_big:
                    selected.extend(list(np.random.choice(
                        super_hot_big,
                        min(big_count, len(super_hot_big), remaining_count),
                        replace=False
                    )))
                else:
                    selected.extend(list(np.random.choice(
                        available_big,
                        min(big_count, len(available_big), remaining_count),
                        replace=False
                    )))
        
        # 如果还不够，随机补充
        remaining_count = 2 - len(selected)
        if remaining_count > 0:
            remaining_candidates = [n for n in precision_candidates if n not in selected]
            if remaining_candidates:
                selected.extend(list(np.random.choice(
                    remaining_candidates,
                    min(remaining_count, len(remaining_candidates)),
                    replace=False
                )))
        
        return selected[:2]
    
    def _apply_kill_filter(self, candidates: List[int], kill_numbers: Dict, ball_type: str) -> List[int]:
        """应用杀号过滤"""
        if not kill_numbers or ball_type not in kill_numbers:
            return candidates[:]
        
        filtered = set(candidates)
        for kill_list in kill_numbers[ball_type]:
            filtered -= set(kill_list)
        
        return list(filtered)
    
    def _evaluate_red_precision(self, combination: List[int]) -> float:
        """评估红球组合的精准度"""
        if len(set(combination)) != 5:
            return 0
        
        score = 0
        
        # 超级热号评分（最重要）
        super_hot_count = len(set(combination) & set(self.super_hot_red))
        score += super_hot_count * 30
        
        # 位置热号评分
        position_hot_count = len(set(combination) & set(self.position_hot_red))
        score += position_hot_count * 15
        
        # 和值评分
        total_sum = sum(combination)
        if self.optimal_red_sum_range[0] <= total_sum <= self.optimal_red_sum_range[1]:
            score += 20
        
        # 跨度评分
        span = max(combination) - min(combination)
        if self.optimal_red_span_range[0] <= span <= self.optimal_red_span_range[1]:
            score += 15
        
        return score


def test_precision_generator():
    """测试精准生成器"""
    generator = PrecisionGenerator()
    
    print("\n测试精准生成器...")
    
    for i in range(3):
        red, blue = generator.generate_precision_numbers(
            "3:2", "2:3", "1:1", seed=i
        )
        print(f"第{i+1}次: 红球{red}, 蓝球{blue}")


if __name__ == "__main__":
    test_precision_generator()
