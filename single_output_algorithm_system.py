#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
单输出算法系统
整理算法确保每个只输出1个结果，衍生出当期/上期/上上期版本，进行历史回测
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from itertools import combinations
import math

# 导入现有的杀号算法
from test_kill_algorithm import KillAlgorithmTester, parse_numbers

class SingleOutputAlgorithmSystem:
    def __init__(self):
        self.data = None
        self.base_tester = KillAlgorithmTester()
        
        # 基础算法定义（每个只输出1个结果）
        self.base_algorithms = {
            'factorial': self._factorial_single,
            'pentagonal': self._pentagonal_single,
            'catalan': self._catalan_single,
            'prime': self._prime_single,
            'fibonacci': self._fibonacci_single,
            'triangular': self._triangular_single,
            'square': self._square_single,
            'cube': self._cube_single,
            'perfect': self._perfect_single,
            'abundant': self._abundant_single,
            # 新增算法
            'lucas': self._lucas_single,
            'hexagonal': self._hexagonal_single,
            'octagonal': self._octagonal_single,
            'mersenne': self._mersenne_single,
            'fermat': self._fermat_single,
            'palindrome': self._palindrome_single,
            'digital_root': self._digital_root_single,
            'sum_digits': self._sum_digits_single,
            'product_digits': self._product_digits_single,
            'reverse_number': self._reverse_number_single
        }
        
        # 衍生算法（每个基础算法 × 3个时间维度）
        self.derived_algorithms = {}
        self._generate_derived_algorithms()

    def load_data(self) -> bool:
        """加载数据"""
        if self.base_tester.load_data():
            self.data = self.base_tester.data
            return True
        return False

    def _generate_derived_algorithms(self):
        """生成衍生算法"""
        for base_name, base_func in self.base_algorithms.items():
            # 当期版本
            self.derived_algorithms[f"{base_name}_current"] = lambda period_data, base_func=base_func: base_func(period_data, 'current')
            # 上期版本
            self.derived_algorithms[f"{base_name}_last"] = lambda period_data, base_func=base_func: base_func(period_data, 'last')
            # 上上期版本
            self.derived_algorithms[f"{base_name}_prev2"] = lambda period_data, base_func=base_func: base_func(period_data, 'prev2')

    def _factorial_single(self, period_data: Dict, time_type: str) -> int:
        """阶乘算法 - 单输出版本"""
        factorials = [1, 2, 6, 24]  # 1!, 2!, 3!, 4!
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-2:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-2:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-2:])
        
        # 选择一个阶乘数作为杀号
        selected_factorial = factorials[period_num % len(factorials)]
        return min(selected_factorial, 35)

    def _pentagonal_single(self, period_data: Dict, time_type: str) -> int:
        """五边形数算法 - 单输出版本"""
        # 五边形数公式: n(3n-1)/2
        pentagonals = []
        for n in range(1, 10):
            pent = n * (3 * n - 1) // 2
            if pent <= 35:
                pentagonals.append(pent)
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-2:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-2:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-2:])
        
        return pentagonals[period_num % len(pentagonals)]

    def _catalan_single(self, period_data: Dict, time_type: str) -> int:
        """卡塔兰数算法 - 单输出版本"""
        # 卡塔兰数: C(n) = (2n)! / ((n+1)! * n!)
        catalans = [1, 1, 2, 5, 14]  # C(0) to C(4)
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])
        
        selected_catalan = catalans[period_num % len(catalans)]
        return min(selected_catalan, 35)

    def _prime_single(self, period_data: Dict, time_type: str) -> int:
        """质数算法 - 单输出版本"""
        primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31]
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-2:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-2:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-2:])
        
        return primes[period_num % len(primes)]

    def _fibonacci_single(self, period_data: Dict, time_type: str) -> int:
        """斐波那契数算法 - 单输出版本"""
        fibs = [1, 1, 2, 3, 5, 8, 13, 21, 34]
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])
        
        return fibs[period_num % len(fibs)]

    def _triangular_single(self, period_data: Dict, time_type: str) -> int:
        """三角数算法 - 单输出版本"""
        # 三角数公式: n(n+1)/2
        triangulars = []
        for n in range(1, 10):
            tri = n * (n + 1) // 2
            if tri <= 35:
                triangulars.append(tri)
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])
        
        return triangulars[period_num % len(triangulars)]

    def _square_single(self, period_data: Dict, time_type: str) -> int:
        """平方数算法 - 单输出版本"""
        squares = [1, 4, 9, 16, 25]  # 1², 2², 3², 4², 5²
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])
        
        return squares[period_num % len(squares)]

    def _cube_single(self, period_data: Dict, time_type: str) -> int:
        """立方数算法 - 单输出版本"""
        cubes = [1, 8, 27]  # 1³, 2³, 3³
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])
        
        return cubes[period_num % len(cubes)]

    def _perfect_single(self, period_data: Dict, time_type: str) -> int:
        """完全数算法 - 单输出版本"""
        perfects = [6, 28]  # 完全数
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])
        
        return perfects[period_num % len(perfects)]

    def _abundant_single(self, period_data: Dict, time_type: str) -> int:
        """过剩数算法 - 单输出版本"""
        abundants = [12, 18, 20, 24, 30]  # 过剩数
        
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])
        
        return abundants[period_num % len(abundants)]

    def _lucas_single(self, period_data: Dict, time_type: str) -> int:
        """卢卡斯数算法 - 单输出版本"""
        lucas = [2, 1, 3, 4, 7, 11, 18, 29]  # 卢卡斯数列

        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])

        return lucas[period_num % len(lucas)]

    def _hexagonal_single(self, period_data: Dict, time_type: str) -> int:
        """六边形数算法 - 单输出版本"""
        # 六边形数公式: n(2n-1)
        hexagonals = []
        for n in range(1, 8):
            hex_num = n * (2 * n - 1)
            if hex_num <= 35:
                hexagonals.append(hex_num)

        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])

        return hexagonals[period_num % len(hexagonals)]

    def _octagonal_single(self, period_data: Dict, time_type: str) -> int:
        """八边形数算法 - 单输出版本"""
        # 八边形数公式: n(3n-2)
        octagonals = []
        for n in range(1, 7):
            oct_num = n * (3 * n - 2)
            if oct_num <= 35:
                octagonals.append(oct_num)

        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])

        return octagonals[period_num % len(octagonals)]

    def _mersenne_single(self, period_data: Dict, time_type: str) -> int:
        """梅森数算法 - 单输出版本"""
        # 梅森数: 2^n - 1
        mersennes = [1, 3, 7, 15, 31]  # 2^1-1, 2^2-1, 2^3-1, 2^4-1, 2^5-1

        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])

        return mersennes[period_num % len(mersennes)]

    def _fermat_single(self, period_data: Dict, time_type: str) -> int:
        """费马数算法 - 单输出版本"""
        # 费马数: 2^(2^n) + 1
        fermats = [3, 5, 17]  # F0, F1, F2

        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-1:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-1:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-1:])

        return fermats[period_num % len(fermats)]

    def _palindrome_single(self, period_data: Dict, time_type: str) -> int:
        """回文数算法 - 单输出版本"""
        palindromes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 22, 33]

        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-2:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-2:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-2:])

        return palindromes[period_num % len(palindromes)]

    def _digital_root_single(self, period_data: Dict, time_type: str) -> int:
        """数字根算法 - 单输出版本"""
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-3:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-3:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-3:])

        # 计算数字根
        while period_num >= 10:
            period_num = sum(int(digit) for digit in str(period_num))

        return max(1, period_num) if period_num <= 35 else period_num % 35 + 1

    def _sum_digits_single(self, period_data: Dict, time_type: str) -> int:
        """数字和算法 - 单输出版本"""
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-3:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-3:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-3:])

        digit_sum = sum(int(digit) for digit in str(period_num))
        return min(digit_sum, 35) if digit_sum > 0 else 1

    def _product_digits_single(self, period_data: Dict, time_type: str) -> int:
        """数字积算法 - 单输出版本"""
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-3:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-3:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-3:])

        digit_product = 1
        for digit in str(period_num):
            digit_product *= int(digit)

        return max(1, min(digit_product, 35)) if digit_product > 0 else 1

    def _reverse_number_single(self, period_data: Dict, time_type: str) -> int:
        """反转数字算法 - 单输出版本"""
        if time_type == 'current':
            period_num = int(str(period_data['current']['期号'])[-2:])
        elif time_type == 'last':
            period_num = int(str(period_data['last']['期号'])[-2:])
        else:  # prev2
            period_num = int(str(period_data['prev2']['期号'])[-2:])

        reversed_num = int(str(period_num)[::-1])
        return min(reversed_num, 35) if reversed_num > 0 else 1

    def test_single_algorithm(self, algo_name: str, test_periods: int = 30) -> Dict:
        """测试单个算法的表现"""
        stats = {
            'total_kills': 0,
            'successful_kills': 0,
            'success_rate': 0.0,
            'details': []
        }
        
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break
                
            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]  # 上一期
            period2_data = self.data.iloc[i + 2]  # 上二期
            
            # 解析号码
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period1_data)
            period2_red, _ = parse_numbers(period2_data)
            
            # 构建期数据
            period_data = {
                'current': current_period,
                'last': period1_data,
                'prev2': period2_data
            }
            
            try:
                # 获取算法杀号
                kill_number = self.derived_algorithms[algo_name](period_data)
                
                if 1 <= kill_number <= 35 and kill_number not in (period1_red + period2_red):
                    stats['total_kills'] += 1
                    is_successful = kill_number not in current_red
                    
                    if is_successful:
                        stats['successful_kills'] += 1
                    
                    stats['details'].append({
                        'period': current_period['期号'],
                        'kill': kill_number,
                        'successful': is_successful,
                        'actual': current_red
                    })
            except Exception as e:
                continue
        
        # 计算成功率
        if stats['total_kills'] > 0:
            stats['success_rate'] = stats['successful_kills'] / stats['total_kills']
        
        return stats

    def test_all_algorithms(self, test_periods: int = 30) -> Dict:
        """测试所有衍生算法"""
        print(f"\n🔍 测试所有单输出算法 (最近{test_periods}期)")
        print("=" * 80)
        
        all_results = {}
        
        for algo_name in self.derived_algorithms.keys():
            print(f"正在测试: {algo_name}")
            result = self.test_single_algorithm(algo_name, test_periods)
            all_results[algo_name] = result
            
            print(f"  结果: 成功率 {result['success_rate']:.1%} ({result['successful_kills']}/{result['total_kills']})")
        
        return all_results

    def find_best_combinations(self, all_results: Dict, combo_size: int = 3, test_periods: int = 30) -> List[Tuple]:
        """找出最佳算法组合"""
        print(f"\n🎯 寻找最佳{combo_size}算法组合")
        print("=" * 80)
        
        # 筛选出有效的算法（成功率>0且有足够的杀号数）
        valid_algorithms = []
        for algo_name, result in all_results.items():
            if result['success_rate'] > 0 and result['total_kills'] >= test_periods * 0.5:  # 至少一半期数有杀号
                valid_algorithms.append(algo_name)
        
        print(f"有效算法数量: {len(valid_algorithms)}")
        
        # 测试所有可能的组合
        best_combinations = []
        
        for combo in combinations(valid_algorithms, combo_size):
            combo_stats = self._test_combination(combo, test_periods)
            best_combinations.append((combo, combo_stats))
        
        # 按全中率排序
        best_combinations.sort(key=lambda x: x[1]['perfect_rate'], reverse=True)
        
        return best_combinations[:20]  # 返回前20个最佳组合

    def _test_combination(self, algorithms: Tuple[str], test_periods: int = 30) -> Dict:
        """测试算法组合的全中率"""
        stats = {
            'total_periods': 0,
            'perfect_periods': 0,  # 所有杀号都成功的期数
            'perfect_rate': 0.0,
            'total_kills': 0,
            'successful_kills': 0,
            'kill_success_rate': 0.0
        }
        
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break
                
            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]
            period2_data = self.data.iloc[i + 2]
            
            # 解析号码
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period1_data)
            period2_red, _ = parse_numbers(period2_data)
            
            # 构建期数据
            period_data = {
                'current': current_period,
                'last': period1_data,
                'prev2': period2_data
            }
            
            # 收集本期所有杀号
            period_kills = []
            for algo_name in algorithms:
                try:
                    kill_number = self.derived_algorithms[algo_name](period_data)
                    if 1 <= kill_number <= 35 and kill_number not in (period1_red + period2_red):
                        period_kills.append(kill_number)
                except Exception:
                    continue
            
            if period_kills:
                stats['total_periods'] += 1
                stats['total_kills'] += len(period_kills)
                
                # 检查杀号成功情况
                successful_kills = 0
                for kill in period_kills:
                    if kill not in current_red:
                        successful_kills += 1
                
                stats['successful_kills'] += successful_kills
                
                # 检查是否全中
                if successful_kills == len(period_kills):
                    stats['perfect_periods'] += 1
        
        # 计算率
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
        
        if stats['total_kills'] > 0:
            stats['kill_success_rate'] = stats['successful_kills'] / stats['total_kills']
        
        return stats

def main():
    """主函数"""
    print("🎯 单输出算法系统")
    print("=" * 60)
    
    # 初始化系统
    system = SingleOutputAlgorithmSystem()
    
    # 加载数据
    if not system.load_data():
        return
    
    print(f"✅ 成功加载数据: {len(system.data)} 期")
    print(f"📊 基础算法数量: {len(system.base_algorithms)}")
    print(f"🔄 衍生算法数量: {len(system.derived_algorithms)}")
    
    # 测试所有算法
    all_results = system.test_all_algorithms(test_periods=30)
    
    # 显示算法表现排行
    print(f"\n📊 算法表现排行榜")
    print("=" * 80)
    
    sorted_results = sorted(all_results.items(), key=lambda x: x[1]['success_rate'], reverse=True)
    
    for i, (algo_name, result) in enumerate(sorted_results, 1):
        status = "🎯" if result['success_rate'] >= 0.9 else "⚠️" if result['success_rate'] >= 0.8 else "❌"
        print(f"  {i:2d}. {algo_name:20} 成功率:{result['success_rate']:6.1%} "
              f"({result['successful_kills']:2d}/{result['total_kills']:2d}) {status}")
    
    # 寻找最佳组合
    best_combinations = system.find_best_combinations(all_results, combo_size=3, test_periods=30)
    
    print(f"\n🏆 最佳3算法组合排行榜 (按全中率排序)")
    print("=" * 80)
    
    for i, (combo, stats) in enumerate(best_combinations[:10], 1):
        status = "🎯" if stats['perfect_rate'] >= 0.8 else "⚠️" if stats['perfect_rate'] >= 0.6 else "❌"
        print(f"  {i:2d}. 全中率:{stats['perfect_rate']:6.1%} "
              f"杀号成功率:{stats['kill_success_rate']:6.1%} "
              f"({stats['perfect_periods']}/{stats['total_periods']}) {status}")
        print(f"      算法组合: {', '.join(combo)}")
    
    print(f"\n🎉 单输出算法系统测试完成！")

if __name__ == "__main__":
    main()
